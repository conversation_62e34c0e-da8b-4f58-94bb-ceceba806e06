use java.util.concurrent.locks.*;
use java.util.concurrent.CountDownLatch;

let x = 0;
let n = 10;

let lk = new ReentrantLock();

let latch = new CountDownLatch(n);

for i in range(0, n) {
  let t = new Thread(lambda() ->
                     lock(lk);
                     x = x + 1;
                     unlock(lk);
                     countDown(latch);
                     p("thread #{i} done");
                     end);
  start(t);
}

await(latch);
p("x=#{x}");


let lk = new ReentrantReadWriteLock();
let wlk = writeLock(lk);
lock(wlk);
x = x + 1;
unlock(wlk);

p("x=#{x}");