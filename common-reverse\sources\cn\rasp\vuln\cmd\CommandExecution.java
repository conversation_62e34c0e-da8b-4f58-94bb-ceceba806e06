package cn.rasp.vuln.cmd;

import java.io.InputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.apache.commons.io.IOUtils;
import sun.misc.Unsafe;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/cmd/CommandExecution.class */
public class CommandExecution {
    public static String processBuild(String cmd) throws Exception {
        Process process = new ProcessBuilder(cmd.split("\\s+")).start();
        process.waitFor();
        return IOUtils.toString(process.getInputStream());
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static String unixProcess(String cmd) throws Exception {
        Class<?> processClass;
        String[] commands = cmd.split("\\s+");
        try {
            processClass = Class.forName("java.lang.UNIXProcess");
        } catch (ClassNotFoundException e) {
            processClass = Class.forName("java.lang.ProcessImpl");
        }
        Constructor<?> constructor = processClass.getDeclaredConstructors()[0];
        constructor.setAccessible(true);
        byte[] bArr = new byte[commands.length - 1];
        int size = bArr.length;
        for (int i = 0; i < bArr.length; i++) {
            bArr[i] = commands[i + 1].getBytes();
            size += bArr[i].length;
        }
        Object argBlock = new byte[size];
        int i2 = 0;
        for (Object[] objArr : bArr) {
            System.arraycopy(objArr, 0, argBlock, i2, objArr.length);
            i2 += objArr.length + 1;
        }
        byte[] bytes = commands[0].getBytes();
        byte[] resultBytes = new byte[bytes.length + 1];
        System.arraycopy(bytes, 0, resultBytes, 0, bytes.length);
        resultBytes[resultBytes.length - 1] = 0;
        Type[] parameterTypes = constructor.getGenericParameterTypes();
        List<Object> argList = new ArrayList<>();
        Object[] objs = {resultBytes, argBlock, Integer.valueOf(bArr.length), null, 1, null, new int[]{-1, -1, -1}, false};
        Collections.addAll(argList, objs);
        if (parameterTypes.length == 9) {
            argList.add(false);
        }
        Object object = constructor.newInstance(argList.toArray(new Object[0]));
        Method inMethod = object.getClass().getDeclaredMethod("getInputStream", new Class[0]);
        inMethod.setAccessible(true);
        return IOUtils.toString((InputStream) inMethod.invoke(object, new Object[0]));
    }

    /* JADX WARN: Multi-variable type inference failed */
    /**
     * 使用反射和Unsafe直接调用系统forkAndExec方法执行命令
     * 这种方法绕过了正常的ProcessBuilder安全检查
     * 
     * @param cmd 要执行的命令字符串
     * @return 命令执行结果
     * @throws Exception 执行过程中的异常
     */
    public static String forkAndExec(String cmd) throws Exception {
        Class processClass;
        // 将命令按空格分割成参数数组
        String[] strs = cmd.split("\\s+");
        
        // 获取Unsafe实例，用于直接分配对象实例
        Field theUnsafeField = Unsafe.class.getDeclaredField("theUnsafe");
        theUnsafeField.setAccessible(true);
        Unsafe unsafe = (Unsafe) theUnsafeField.get(null);
        
        // 尝试获取UNIXProcess类，如果不存在则使用ProcessImpl
        try {
            processClass = Class.forName("java.lang.UNIXProcess");
        } catch (ClassNotFoundException e) {
            processClass = Class.forName("java.lang.ProcessImpl");
        }
        
        // 使用Unsafe直接创建Process对象实例，绕过构造函数
        Object processObject = unsafe.allocateInstance(processClass);
        
        // 处理命令参数，将除第一个参数外的所有参数转换为字节数组
        byte[] bArr = new byte[strs.length - 1];
        int size = bArr.length;
        for (int i = 0; i < bArr.length; i++) {
            bArr[i] = strs[i + 1].getBytes();
            size += bArr[i].length;
        }
        
        // 构建参数块，将所有参数连接成一个字节数组
        byte[] argBlock = new byte[size];
        int i2 = 0;
        for (Object[] objArr : bArr) {
            System.arraycopy(objArr, 0, argBlock, i2, objArr.length);
            i2 += objArr.length + 1;
        }
        
        // 初始化环境变量数组和标准文件描述符数组
        int[] envc = new int[1];
        int[] std_fds = {-1, -1, -1}; // stdin, stdout, stderr
        
        // 获取Process类的关键字段
        Field launchMechanismField = processClass.getDeclaredField("launchMechanism");
        Field helperpathField = processClass.getDeclaredField("helperpath");
        launchMechanismField.setAccessible(true);
        helperpathField.setAccessible(true);
        
        // 获取启动机制对象和辅助路径
        Object launchMechanismObject = launchMechanismField.get(processObject);
        byte[] helperpathObject = (byte[]) helperpathField.get(processObject);
        
        // 获取启动机制的序号
        int ordinal = ((Integer) launchMechanismObject.getClass().getMethod("ordinal", new Class[0]).invoke(launchMechanismObject, new Object[0])).intValue();
        
        // 尝试调用forkAndExec方法执行命令
        try {
            // 查找并调用包含"forkAndExec"的方法名（可能是私有方法）
            for (Method m : processClass.getDeclaredMethods()) {
                if (m.getName().endsWith("forkAndExec") && !m.getName().equals("forkAndExec")) {
                    m.setAccessible(true);
                    m.invoke(processObject, Integer.valueOf(ordinal + 1), helperpathObject, toCString(strs[0]), argBlock, Integer.valueOf(bArr.length), null, Integer.valueOf(envc[0]), null, std_fds, false);
                }
            }
        } catch (Exception e2) {
            // 如果上面的方法失败，尝试直接调用标准的forkAndExec方法
            Method forkMethod = processClass.getDeclaredMethod("forkAndExec", Integer.TYPE, byte[].class, byte[].class, byte[].class, Integer.TYPE, byte[].class, Integer.TYPE, byte[].class, int[].class, Boolean.TYPE);
            forkMethod.setAccessible(true);
            forkMethod.invoke(processObject, Integer.valueOf(ordinal + 1), helperpathObject, toCString(strs[0]), argBlock, Integer.valueOf(bArr.length), null, Integer.valueOf(envc[0]), null, std_fds, false);
        }
        
        // 初始化进程的输入输出流
        Method initStreamsMethod = processClass.getDeclaredMethod("initStreams", int[].class);
        initStreamsMethod.setAccessible(true);
        initStreamsMethod.invoke(processObject, std_fds);
        
        // 获取进程的输入流并读取结果
        Method getInputStreamMethod = processClass.getMethod("getInputStream", new Class[0]);
        getInputStreamMethod.setAccessible(true);
        InputStream in = (InputStream) getInputStreamMethod.invoke(processObject, new Object[0]);
        
        // 将输入流转换为字符串并返回
        return IOUtils.toString(in);
    }

    private static byte[] toCString(String s) {
        if (s == null) {
            return null;
        }
        byte[] bytes = s.getBytes();
        byte[] result = new byte[bytes.length + 1];
        System.arraycopy(bytes, 0, result, 0, bytes.length);
        result[result.length - 1] = 0;
        return result;
    }
}
