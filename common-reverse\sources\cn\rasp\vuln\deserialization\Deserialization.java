package cn.rasp.vuln.deserialization;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.parser.ParserConfig;
import com.caucho.hessian.io.HessianInput;
import com.cedarsoftware.util.io.JsonReader;
import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.yamlbeans.YamlConfig;
import com.esotericsoftware.yamlbeans.YamlReader;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.thoughtworks.xstream.XStream;
import flex.messaging.io.SerializationContext;
import flex.messaging.io.amf.ActionContext;
import flex.messaging.io.amf.ActionMessage;
import flex.messaging.io.amf.AmfTrace;
import flex.messaging.io.amfx.AmfxMessageDeserializer;
import java.beans.XMLDecoder;
import java.io.ByteArrayInputStream;
import java.io.ObjectInputStream;
import java.io.StringReader;
import java.net.URLDecoder;
import org.apache.commons.codec.binary.Base64;
import org.apache.mina.core.buffer.IoBuffer;
import org.exolab.castor.xml.Unmarshaller;
import org.exolab.castor.xml.XMLContext;
import org.red5.io.amf3.Input;
import org.red5.io.object.Deserializer;
import org.yaml.snakeyaml.Yaml;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/deserialization/Deserialization.class */
public class Deserialization {
    public static void readObject(String payload) throws Exception {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(Base64.decodeBase64(payload));
        ObjectInputStream bis = new ObjectInputStream(inputStream);
        bis.readObject();
    }

    public static void fastJson(String payload) throws Exception {
        JSON.parseObject(payload, Object.class, new ParserConfig(), new Feature[]{Feature.SupportNonPublicField});
    }

    public static void jackson(String payload) throws Exception {
        new ObjectMapper().enableDefaultTyping().readValue(payload, Object.class);
    }

    public static void sneakYaml(String payload) throws Exception {
        Yaml yaml = new Yaml();
        yaml.load(URLDecoder.decode(payload));
    }

    public static void jYAML(String payload) {
        org.ho.yaml.Yaml.loadType(payload, Object.class);
    }

    public static Object yamlBeans(String payload) throws Exception {
        YamlConfig yc = new YamlConfig();
        YamlReader r = new YamlReader(payload, yc);
        return r.read();
    }

    public static Object blazeDSAMF(String payload) throws Exception {
        SerializationContext sc = new SerializationContext();
        AmfxMessageDeserializer amfxMessageDeserializer = new AmfxMessageDeserializer();
        amfxMessageDeserializer.initialize(sc, new ByteArrayInputStream(payload.getBytes()), (AmfTrace) null);
        ActionMessage m = new ActionMessage();
        amfxMessageDeserializer.readMessage(m, (ActionContext) null);
        return m.getHeader(0).getData();
    }

    public static Object red5IO(String payload) {
        IoBuffer buf = IoBuffer.wrap(Base64.decodeBase64(payload));
        return Deserializer.deserialize(new Input(buf), Object.class);
    }

    public static Object castor(String payload) throws Exception {
        XMLContext context = new XMLContext();
        Unmarshaller unmarshaller = context.createUnmarshaller();
        return unmarshaller.unmarshal(new StringReader(payload));
    }

    public static Object xStream(String payload) throws Exception {
        XStream xStream = new XStream();
        return xStream.fromXML(payload);
    }

    public static void xmlDecoder(String payload) throws Exception {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(payload.getBytes());
        XMLDecoder xmlDecoder = new XMLDecoder(inputStream);
        xmlDecoder.readObject();
        xmlDecoder.close();
    }

    public static void kryo(String payload) {
        Kryo k = new Kryo();
        com.esotericsoftware.kryo.io.Input in = new com.esotericsoftware.kryo.io.Input(Base64.decodeBase64(payload));
        k.readClassAndObject(in);
        in.close();
    }

    public static void hessian(String payload) throws Exception {
        ByteArrayInputStream bis = new ByteArrayInputStream(Base64.decodeBase64(payload));
        new HessianInput(bis).readObject();
    }

    public static void jsonIO(String payload) throws Exception {
        JsonReader.jsonToJava(payload);
    }
}
