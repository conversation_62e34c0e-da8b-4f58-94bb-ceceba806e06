package cn.rasp.vuln.expression;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import de.odysseus.el.ExpressionFactoryImpl;
import de.odysseus.el.util.SimpleContext;
import groovy.lang.GroovyShell;
import java.io.InputStream;
import java.lang.reflect.Method;
import javax.el.ValueExpression;
import ognl.Ognl;
import ognl.OgnlContext;
import org.apache.commons.io.IOUtils;
import org.apache.commons.jexl2.Expression;
import org.apache.commons.jexl2.JexlEngine;
import org.apache.commons.jexl2.MapContext;
import org.apache.commons.jexl3.JexlBuilder;
import org.apache.commons.jexl3.JexlExpression;
import org.apache.commons.jxpath.JXPathContext;
import org.mvel2.MVEL;
import org.springframework.expression.spel.standard.SpelExpressionParser;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/expression/ExpParser.class */
public class ExpParser {
    public static Object ognl(String exp) throws Exception {
        OgnlContext context = new OgnlContext();
        return Ognl.getValue(exp, context, context.getRoot());
    }

    public static Object spEL(String exp) {
        return new SpelExpressionParser().parseExpression(exp).getValue();
    }

    public static Object mvel(String exp) {
        return MVEL.eval(exp);
    }

    private static Object getValueFormProcess(Object object) {
        try {
            Method inMethod = object.getClass().getDeclaredMethod("getInputStream", new Class[0]);
            inMethod.setAccessible(true);
            return IOUtils.toString((InputStream) inMethod.invoke(object, new Object[0]));
        } catch (Exception e) {
            return object;
        }
    }

    public static Object groovy(String exp) {
        return getValueFormProcess(new GroovyShell().evaluate(exp));
    }

    public static Object el(String exp) {
        ExpressionFactoryImpl expressionFactoryImpl = new ExpressionFactoryImpl();
        SimpleContext simpleContext = new SimpleContext();
        ValueExpression valueExpression = expressionFactoryImpl.createValueExpression(simpleContext, exp, String.class);
        return valueExpression.getValue(simpleContext);
    }

    public static Object jexl2(String exp) {
        JexlEngine jexl = new JexlEngine();
        Expression e = jexl.createExpression(exp);
        MapContext jc = new MapContext();
        return getValueFormProcess(e.evaluate(jc));
    }

    public static Object jexl3(String exp) {
        org.apache.commons.jexl3.JexlEngine jexl = new JexlBuilder().create();
        JexlExpression e = jexl.createExpression(exp);
        return getValueFormProcess(e.evaluate(new org.apache.commons.jexl3.MapContext()));
    }

    public static Object jxpath(String exp) {
        JXPathContext context = JXPathContext.newContext((Object) null);
        return getValueFormProcess(context.getValue(exp));
    }

    public static Object aviator(String content) {
        AviatorEvaluatorInstance evaluator = AviatorEvaluator.newInstance();
        return evaluator.execute(content);
    }
}
