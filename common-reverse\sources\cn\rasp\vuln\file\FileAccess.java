package cn.rasp.vuln.file;

import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.lang.reflect.Array;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.nio.file.CopyOption;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import sun.misc.Unsafe;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/file/FileAccess.class */
public class FileAccess {

    /* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/file/FileAccess$IOFile.class */
    public static class IOFile {
        public static String read(String filePath) throws IOException {
            FileInputStream inputStream = new FileInputStream(filePath);
            Throwable th = null;
            try {
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                Throwable th2 = null;
                try {
                    try {
                        byte[] buffer = new byte[1024];
                        while (true) {
                            int bytesRead = inputStream.read(buffer);
                            if (bytesRead == -1) {
                                break;
                            }
                            outputStream.write(buffer, 0, bytesRead);
                        }
                        String byteArrayOutputStream = outputStream.toString("UTF-8");
                        if (outputStream != null) {
                            if (0 != 0) {
                                try {
                                    outputStream.close();
                                } catch (Throwable th3) {
                                    th2.addSuppressed(th3);
                                }
                            } else {
                                outputStream.close();
                            }
                        }
                        return byteArrayOutputStream;
                    } catch (Throwable th4) {
                        if (outputStream != null) {
                            if (th2 != null) {
                                try {
                                    outputStream.close();
                                } catch (Throwable th5) {
                                    th2.addSuppressed(th5);
                                }
                            } else {
                                outputStream.close();
                            }
                        }
                        throw th4;
                    }
                } finally {
                }
            } finally {
                if (inputStream != null) {
                    if (0 != 0) {
                        try {
                            inputStream.close();
                        } catch (Throwable th6) {
                            th.addSuppressed(th6);
                        }
                    } else {
                        inputStream.close();
                    }
                }
            }
        }

        public static boolean write(String filePath, String content) throws IOException {
            File file = new File(filePath);
            FileOutputStream fos = new FileOutputStream(file);
            Throwable th = null;
            try {
                try {
                    fos.write(content.getBytes(StandardCharsets.UTF_8));
                    String read = read(filePath);
                    boolean equals = Objects.equals(read, content);
                    if (fos != null) {
                        if (0 != 0) {
                            try {
                                fos.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            fos.close();
                        }
                    }
                    return equals;
                } finally {
                }
            } catch (Throwable th3) {
                if (fos != null) {
                    if (th != null) {
                        try {
                            fos.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        fos.close();
                    }
                }
                throw th3;
            }
        }

        public static void unsafeWrite(String fileName, String content) throws Exception {
            Field field = Unsafe.class.getDeclaredField("theUnsafe");
            field.setAccessible(true);
            Unsafe unsafe = (Unsafe) field.get(null);
            FileOutputStream fileOutputStream = (FileOutputStream) unsafe.allocateInstance(FileOutputStream.class);
            File file = new File(fileName);
            String name = file.getPath();
            SecurityManager security = System.getSecurityManager();
            if (security != null) {
                security.checkWrite(name);
            }
            Field fdField = FileOutputStream.class.getDeclaredField("fd");
            fdField.setAccessible(true);
            FileDescriptor fd = new FileDescriptor();
            fdField.set(fileOutputStream, fd);
            Method attachMethod = FileDescriptor.class.getDeclaredMethod("attach", Closeable.class);
            attachMethod.setAccessible(true);
            attachMethod.invoke(fd, fileOutputStream);
            Field appendFiled = FileOutputStream.class.getDeclaredField("append");
            appendFiled.setAccessible(true);
            appendFiled.set(fileOutputStream, true);
            Field pathField = FileOutputStream.class.getDeclaredField("path");
            pathField.setAccessible(true);
            pathField.set(fileOutputStream, name);
            Method openMethod = FileOutputStream.class.getDeclaredMethod("open", String.class, Boolean.TYPE);
            openMethod.setAccessible(true);
            openMethod.invoke(fileOutputStream, name, true);
            fileOutputStream.write(content.getBytes(StandardCharsets.UTF_8));
        }

        @Deprecated
        public static boolean delete(String filePath) throws IOException {
            File file = new File(filePath);
            return file.delete();
        }

        @Deprecated
        public static boolean rename(String source, String dest) {
            File sourceFile = new File(source);
            File destFile = new File(dest);
            return sourceFile.renameTo(destFile);
        }

        @Deprecated
        public static String[] list(String dir) {
            return new File(dir).list();
        }
    }

    /* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/file/FileAccess$FileSystem.class */
    public static class FileSystem {
        private static Object getUnixFileSystem() throws Exception {
            Class<?> unixFileSystemClass = Class.forName("java.io.UnixFileSystem");
            Constructor<?> declaredConstructor = unixFileSystemClass.getDeclaredConstructor(new Class[0]);
            declaredConstructor.setAccessible(true);
            return declaredConstructor.newInstance(new Object[0]);
        }

        public static boolean delete(String filePath) throws Exception {
            Class<?> unixFileSystemClass = Class.forName("java.io.UnixFileSystem");
            Method delete = unixFileSystemClass.getDeclaredMethod("delete", File.class);
            delete.setAccessible(true);
            delete.invoke(getUnixFileSystem(), new File(filePath));
            return Files.notExists(Paths.get(filePath, new String[0]), new LinkOption[0]);
        }

        public static boolean rename(String source, String dest) throws Exception {
            Class<?> unixFileSystem = Class.forName("java.io.UnixFileSystem");
            Method rename = unixFileSystem.getDeclaredMethod("rename", File.class, File.class);
            rename.setAccessible(true);
            rename.invoke(getUnixFileSystem(), new File(source), new File(dest));
            return Files.exists(Paths.get(dest, new String[0]), new LinkOption[0]);
        }

        public static String[] list(String dir) throws Exception {
            Class<?> unixFileSystem = Class.forName("java.io.UnixFileSystem");
            Method method = unixFileSystem.getMethod("list", File.class);
            method.setAccessible(true);
            Object list = method.invoke(getUnixFileSystem(), new File(dir));
            return (String[]) list;
        }

        public static String[] listRoot() throws Exception {
            Class<?> unixFileSystem = Class.forName("java.io.UnixFileSystem");
            Method method = unixFileSystem.getMethod("listRoots", new Class[0]);
            method.setAccessible(true);
            Object listRoots = method.invoke(getUnixFileSystem(), new Object[0]);
            List<String> result = new ArrayList<>();
            for (File file : (File[]) listRoots) {
                result.add(file.getAbsolutePath());
            }
            return (String[]) result.toArray(new String[0]);
        }
    }

    /* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/file/FileAccess$NIOFile.class */
    public static class NIOFile {
        public static String readAllBytes(String filePath) throws IOException {
            byte[] bytes = Files.readAllBytes(Paths.get(filePath, new String[0]));
            return new String(bytes);
        }

        public static String readAllLines(String filePath) throws IOException {
            List<String> strings = Files.readAllLines(Paths.get(filePath, new String[0]), StandardCharsets.UTF_8);
            return Arrays.toString(strings.toArray());
        }

        public static boolean createFile(String filePath) throws IOException {
            Path path = Paths.get(filePath, new String[0]);
            Path file = Files.createFile(path, new FileAttribute[0]);
            return Files.exists(file, new LinkOption[0]);
        }

        @Deprecated
        public static boolean move(String source, String dest) throws IOException {
            Path target = Files.move(Paths.get(source, new String[0]), Paths.get(dest, new String[0]), new CopyOption[0]);
            return Files.exists(target, new LinkOption[0]);
        }

        @Deprecated
        public static boolean copy(String source, String dest) throws IOException {
            Path target = Files.copy(Paths.get(source, new String[0]), Paths.get(dest, new String[0]), new CopyOption[0]);
            return Files.exists(target, new LinkOption[0]);
        }

        public static boolean delete(String filePath) throws Exception {
            Path path = Paths.get(filePath, new String[0]);
            Class<?> unixNativeDispatcher = Class.forName("sun.nio.fs.UnixNativeDispatcher");
            Class<?> unixPath = Class.forName("sun.nio.fs.UnixPath");
            Method unlink = unixNativeDispatcher.getDeclaredMethod("unlink", unixPath);
            unlink.setAccessible(true);
            unlink.invoke(null, path);
            return Files.notExists(path, new LinkOption[0]);
        }

        public static boolean deleteIfExists(String filePath) throws IOException {
            Path path = Paths.get(filePath, new String[0]);
            return Files.deleteIfExists(path);
        }
    }

    /* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/file/FileAccess$RandomFile.class */
    public static class RandomFile {
        public static String read(String filePath) throws IOException {
            RandomAccessFile file = new RandomAccessFile(filePath, "r");
            Throwable th = null;
            try {
                try {
                    long fileLength = file.length();
                    byte[] data = new byte[(int) fileLength];
                    file.readFully(data);
                    String str = new String(data);
                    if (file != null) {
                        if (0 != 0) {
                            try {
                                file.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            file.close();
                        }
                    }
                    return str;
                } catch (Throwable th3) {
                    if (file != null) {
                        if (th != null) {
                            try {
                                file.close();
                            } catch (Throwable th4) {
                                th.addSuppressed(th4);
                            }
                        } else {
                            file.close();
                        }
                    }
                    throw th3;
                }
            } finally {
            }
        }

        public static boolean write(String filePath, String content) throws IOException {
            RandomAccessFile file = new RandomAccessFile(filePath, "rw");
            Throwable th = null;
            try {
                file.seek(file.length());
                file.writeBytes(content);
                String read = read(filePath);
                boolean equals = Objects.equals(read, content);
                if (file != null) {
                    if (0 != 0) {
                        try {
                            file.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        file.close();
                    }
                }
                return equals;
            } catch (Throwable th3) {
                if (file != null) {
                    if (0 != 0) {
                        try {
                            file.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        file.close();
                    }
                }
                throw th3;
            }
        }
    }

    /* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/file/FileAccess$UnixCopy.class */
    public static class UnixCopy {
        private static Path invoke(String methodName, String source, String dest) throws Exception {
            Class<?> unixCopyFile = Class.forName("sun.nio.fs.UnixCopyFile");
            Class<?> unixPath = Class.forName("sun.nio.fs.UnixPath");
            Method method = unixCopyFile.getDeclaredMethod(methodName, unixPath, unixPath, CopyOption[].class);
            method.setAccessible(true);
            Class<?> copyOptionClass = Class.forName("java.nio.file.CopyOption");
            Object copyOptionArray = Array.newInstance(copyOptionClass, 0);
            Path target = Paths.get(dest, new String[0]);
            method.invoke(null, Paths.get(source, new String[0]), target, copyOptionArray);
            return target;
        }

        public static boolean move(String source, String dest) throws Exception {
            Path target = invoke("move", source, dest);
            return Files.exists(target, new LinkOption[0]);
        }

        public static boolean copy(String source, String dest) throws Exception {
            Path target = invoke("copy", source, dest);
            return Files.exists(target, new LinkOption[0]);
        }
    }
}
