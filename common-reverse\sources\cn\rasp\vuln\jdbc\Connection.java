package cn.rasp.vuln.jdbc;

import java.sql.DriverManager;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/jdbc/Connection.class */
public class Connection {
    public static void getConnection(String url) throws Exception {
        java.sql.Connection connection = DriverManager.getConnection(url);
        connection.close();
    }

    public static void h2(String url) throws Exception {
        Class.forName("org.h2.Driver");
        getConnection(url);
    }

    public static void mysql(String url) throws Exception {
        Class.forName("com.mysql.jdbc.Driver");
        getConnection(url);
    }

    public static void db2(String url) throws Exception {
        Class.forName("com.ibm.db2.jcc.DB2Driver");
        getConnection(url);
    }

    public static void derby(String host) throws Exception {
        Class.forName("org.apache.derby.jdbc.EmbeddedDriver");
        getConnection("****************************");
        getConnection("*******************************************=" + host);
    }

    public static void modeshape(String url) throws Exception {
        Class.forName("org.modeshape.jdbc.LocalJcrDriver");
        getConnection(url);
    }

    public static void postgres(String url) throws Exception {
        Class.forName("org.postgresql.Driver");
        getConnection(url);
    }

    public static void oracle(String url) throws Exception {
        Class.forName("oracle.jdbc.driver.OracleDriver");
        getConnection(url);
    }

    public static void mssql(String url) throws Exception {
        Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        getConnection(url);
    }

    public static void mariadb(String url) throws Exception {
        Class.forName("org.mariadb.jdbc.Driver");
        getConnection(url);
    }
}
