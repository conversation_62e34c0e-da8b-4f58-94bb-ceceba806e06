package cn.rasp.vuln.memshell.springmvc;

import java.lang.reflect.Field;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.GenericApplicationListenerAdapter;
import org.springframework.context.event.SourceFilteringListener;
import org.springframework.context.support.AbstractApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.servlet.DispatcherServlet;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/springmvc/Util.class */
public class Util {
    public DispatcherServlet getServlet() {
        AbstractApplicationContext webApplicationContext = (AbstractApplicationContext) RequestContextHolder.currentRequestAttributes().getAttribute("org.springframework.web.servlet.DispatcherServlet.CONTEXT", 0);
        DispatcherServlet servlet = null;
        try {
            servlet = (DispatcherServlet) webApplicationContext.getBean("dispatcherServlet");
        } catch (Exception e) {
            try {
                for (ApplicationListener applicationListener : webApplicationContext.getApplicationListeners()) {
                    if (applicationListener instanceof SourceFilteringListener) {
                        GenericApplicationListenerAdapter gl = (GenericApplicationListenerAdapter) getFieldValue(applicationListener, "delegate");
                        Object delegate = getFieldValue(gl, "delegate");
                        if (delegate.getClass().getName().equals("org.springframework.web.servlet.FrameworkServlet$ContextRefreshListener")) {
                            servlet = (DispatcherServlet) getFieldValue(delegate, "this$0");
                        }
                    }
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        return servlet;
    }

    public static Field getField(Class clazz, String fieldName) {
        try {
            Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field;
        } catch (Exception e) {
            return null;
        }
    }

    public static Object getFieldValue(Object obj, String fieldName) throws IllegalAccessException {
        Field targetField;
        Class clazz = obj.getClass();
        Field field = getField(clazz, fieldName);
        while (true) {
            targetField = field;
            if (targetField != null || clazz == Object.class) {
                break;
            }
            clazz = clazz.getSuperclass();
            field = getField(clazz, fieldName);
        }
        if (targetField != null) {
            return targetField.get(obj);
        }
        return null;
    }
}
