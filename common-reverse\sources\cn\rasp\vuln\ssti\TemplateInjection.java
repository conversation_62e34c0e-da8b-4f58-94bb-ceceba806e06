package cn.rasp.vuln.ssti;

import com.google.common.collect.Maps;
import com.hubspot.jinjava.Jinjava;
import freemarker.template.Configuration;
import freemarker.template.Template;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/ssti/TemplateInjection.class */
public class TemplateInjection {
    public static String velocity(String payload) {
        StringWriter sw = new StringWriter();
        Velocity.evaluate(new VelocityContext(), sw, "tag", payload);
        return sw.toString();
    }

    public static String freemarker(String payload) throws Exception {
        Configuration cfg = new Configuration();
        cfg.setDefaultEncoding("UTF-8");
        Map<String, Object> input = new HashMap<>();
        input.put("object", new Object());
        Template template = new Template("templateName", new StringReader(payload), cfg);
        StringWriter output = new StringWriter();
        template.process(input, output);
        return output.toString();
    }

    public static String jinJava(String payload) {
        Jinjava jnj = new Jinjava();
        Map<String, Object> context = Maps.newHashMap();
        return jnj.render(payload, context);
    }
}
