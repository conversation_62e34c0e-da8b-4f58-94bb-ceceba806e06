package cn.rasp.vuln.xpath;

import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/xpath/XPathSearch.class */
public class XPathSearch {
    public static List<String> search(String username, String password) throws Exception {
        Document doc = DocumentBuilderFactory.newInstance().newDocumentBuilder().parse(new InputSource(new StringReader("<users><user><username>admin</username><password>123</password></user><user><username>root</username><password>456</password></user></users>")));
        XPath xpath = XPathFactory.newInstance().newXPath();
        NodeList nodes = (NodeList) xpath.evaluate("/users/user[username='" + username + "' and password='" + password + "']", doc, XPathConstants.NODESET);
        int length = nodes.getLength();
        List<String> result = new ArrayList<>();
        for (int i = 0; i < length; i++) {
            NodeList childNodes = nodes.item(i).getChildNodes();
            int length1 = childNodes.getLength();
            for (int j = 0; j < length1; j++) {
                result.add(childNodes.item(j).getNodeName() + ":" + childNodes.item(j).getTextContent());
            }
        }
        return result;
    }
}
