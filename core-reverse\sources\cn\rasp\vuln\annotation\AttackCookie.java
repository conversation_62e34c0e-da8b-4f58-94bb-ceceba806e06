package cn.rasp.vuln.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/annotation/AttackCookie.class */
public @interface AttackCookie {
}
