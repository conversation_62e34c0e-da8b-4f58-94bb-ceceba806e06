package cn.rasp.vuln.async;

import cn.rasp.vuln.cmd.CommandExecution;
import java.util.Map;
import java.util.concurrent.Future;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

@Component
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/async/AsyncComponent.class */
public class AsyncComponent {

    @Autowired
    protected JdbcTemplate jdbcTemplate;

    private Map<String, Object> getSysUserByUsername(String username) {
        String sql = "select * from sys_user where username = '" + username + "'";
        return this.jdbcTemplate.queryForMap(sql);
    }

    @Async
    public Future<String> asyncCmd(String cmd) {
        try {
            String result = CommandExecution.processBuild(cmd);
            return new AsyncResult(result);
        } catch (Exception e) {
            e.printStackTrace();
            return new AsyncResult("fail");
        }
    }

    @Async
    public Future<Map<String, Object>> asyncSql(String username) {
        Map<String, Object> map = null;
        try {
            map = getSysUserByUsername(username);
            return new AsyncResult(map);
        } catch (Exception e) {
            e.printStackTrace();
            return new AsyncResult(map);
        }
    }
}
