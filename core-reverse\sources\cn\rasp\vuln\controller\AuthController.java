package cn.rasp.vuln.controller;

import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping({"/auth"})
@Controller
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/AuthController.class */
public class AuthController {
    @GetMapping({"/login"})
    public void login(String url, HttpServletResponse response) throws IOException {
        response.sendRedirect(url);
    }
}
