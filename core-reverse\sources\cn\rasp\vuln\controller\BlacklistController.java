package cn.rasp.vuln.controller;

import cn.rasp.vuln.utils.HttpServletRequestUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/blacklist/"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/BlacklistController.class */
public class BlacklistController {
    @RequestMapping({"/ip"})
    public ResponseEntity<?> ip() {
        return ResponseEntity.ok(HttpServletRequestUtils.getRemoteAddr());
    }

    @RequestMapping({"/url"})
    public ResponseEntity<?> url() {
        return ResponseEntity.ok("Hello I'm fine, maybe blacklist is not working");
    }
}
