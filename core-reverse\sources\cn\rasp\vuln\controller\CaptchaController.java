package cn.rasp.vuln.controller;

import com.wf.captcha.utils.CaptchaUtil;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/CaptchaController.class */
public class CaptchaController {
    @RequestMapping({"/captcha"})
    public void captcha(HttpServletRequest request, HttpServletResponse response) throws Exception {
        CaptchaUtil.out(request, response);
    }
}
