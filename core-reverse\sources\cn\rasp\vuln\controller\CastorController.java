package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractPostBodyController;
import cn.rasp.vuln.deserialization.Deserialization;
import cn.rasp.vuln.entity.AttackPayload;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: DeserializationController.java */
@RequestMapping({"/deserialization/castor"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/CastorController.class */
class CastorController extends AbstractPostBodyController {
    CastorController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        Deserialization.castor(payload.getPayload());
        return ResponseEntity.ok("反序列化成功");
    }
}
