package cn.rasp.vuln.controller;

import cn.rasp.vuln.cmd.CommandExecution;
import cn.rasp.vuln.controller.common.AbstractFullController;
import cn.rasp.vuln.entity.AttackPayload;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/cmd/process_builder"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/CmdController.class */
public class CmdController extends AbstractFullController {
    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(CommandExecution.processBuild(payload.getPayload()));
    }
}
