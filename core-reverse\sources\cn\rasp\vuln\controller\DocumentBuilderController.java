package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractPostBodyController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.xxe.XMLParser;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: XXEController.java */
@RequestMapping({"/xxe/document_builder"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/DocumentBuilderController.class */
class DocumentBuilderController extends AbstractPostBodyController {
    DocumentBuilderController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(XMLParser.documentBuilder(payload.getPayload()));
    }
}
