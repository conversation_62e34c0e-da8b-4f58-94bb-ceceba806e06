package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractPostBodyController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.ssti.TemplateInjection;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/* compiled from: SSTIController.java */
@RequestMapping({"/ssti/freemarker"})
@Controller
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/FreeMarkerController.class */
class FreeMarkerController extends AbstractPostBodyController {
    FreeMarkerController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(TemplateInjection.freemarker(payload.getPayload()));
    }
}
