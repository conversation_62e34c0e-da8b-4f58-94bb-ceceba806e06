package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractPostBodyController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.expression.ExpParser;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: ExpressionController.java */
@RequestMapping({"/expression/groovy"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/GroovyController.class */
class GroovyController extends AbstractPostBodyController {
    GroovyController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(ExpParser.groovy(payload.getPayload()));
    }
}
