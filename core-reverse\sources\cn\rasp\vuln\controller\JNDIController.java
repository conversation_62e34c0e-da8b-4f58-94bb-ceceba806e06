package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractCommonController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.jndi.JNDI;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/jndi"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/JNDIController.class */
public class JNDIController extends AbstractCommonController {
    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(JNDI.lookup(payload.getPayload()));
    }
}
