package cn.rasp.vuln.controller;

import java.security.AccessController;
import java.security.PrivilegedAction;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/JNIController.class */
public class JNIController {
    @RequestMapping({"/jni/load"})
    public void load(String payload) {
        System.load(payload);
    }

    /* renamed from: cn.rasp.vuln.controller.JNIController$1 */
    /* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/JNIController$1.class */
    class AnonymousClass1 implements PrivilegedAction<Void> {
        AnonymousClass1() {
        }

        @Override // java.security.PrivilegedAction
        public Void run() {
            System.loadLibrary("fontmanager");
            return null;
        }
    }

    @RequestMapping({"/jni/load/jdk"})
    public void loadJdk() {
        try {
            AccessController.doPrivileged(new PrivilegedAction<Void>() { // from class: cn.rasp.vuln.controller.JNIController.1
                AnonymousClass1() {
                }

                @Override // java.security.PrivilegedAction
                public Void run() {
                    System.loadLibrary("fontmanager");
                    return null;
                }
            });
        } catch (SecurityException e) {
            e.printStackTrace();
        }
    }
}
