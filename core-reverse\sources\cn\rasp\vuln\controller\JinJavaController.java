package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractPostBodyController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.ssti.TemplateInjection;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/* compiled from: SSTIController.java */
@RequestMapping({"/ssti/jinjava"})
@Controller
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/JinJavaController.class */
class JinJavaController extends AbstractPostBodyController {
    JinJavaController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(TemplateInjection.jinJava(payload.getPayload()));
    }
}
