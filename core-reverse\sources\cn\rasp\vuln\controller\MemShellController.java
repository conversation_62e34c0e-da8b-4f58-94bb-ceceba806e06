package cn.rasp.vuln.controller;

import cn.rasp.vuln.memshell.springmvc.ControllerHandlerShell;
import cn.rasp.vuln.memshell.springmvc.HandlerAdapterShell;
import cn.rasp.vuln.memshell.springmvc.HandlerMethodShell;
import cn.rasp.vuln.memshell.springmvc.HttpRequestHandlerShell;
import cn.rasp.vuln.memshell.springmvc.InterceptorShell;
import cn.rasp.vuln.memshell.springmvc.MultipartResolverDelegateShell;
import cn.rasp.vuln.memshell.springmvc.ServletHandlerShell;
import cn.rasp.vuln.memshell.tomcat.FilterShell;
import cn.rasp.vuln.memshell.tomcat.ListenerShell;
import cn.rasp.vuln.utils.HttpServletRequestUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/memshell/"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/MemShellController.class */
public class MemShellController {
    @RequestMapping({"/spring/controller"})
    public ResponseEntity<String> springControllerMemShell() throws Exception {
        String path = HandlerMethodShell.addShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + path);
    }

    @RequestMapping({"/spring/controller1"})
    public ResponseEntity<String> springController1MemShell() throws Exception {
        String path = ControllerHandlerShell.addShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + path);
    }

    @RequestMapping({"/spring/controller2"})
    public ResponseEntity<String> springController2MemShell() throws Exception {
        String path = HttpRequestHandlerShell.addShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + path);
    }

    @RequestMapping({"/spring/controller3"})
    public ResponseEntity<String> springController3MemShell() throws Exception {
        String path = ServletHandlerShell.addShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + path);
    }

    @RequestMapping({"/spring/controller4"})
    public ResponseEntity<String> springController4MemShell() throws Exception {
        String path = HandlerAdapterShell.addShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + path);
    }

    @RequestMapping({"/spring/interceptor"})
    public ResponseEntity<String> springInterceptMemShell() throws Exception {
        InterceptorShell.addInterceptorShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + "/index/hello");
    }

    @RequestMapping({"/spring/multipart"})
    public ResponseEntity<String> springMultipartMemShell() throws Exception {
        String path = MultipartResolverDelegateShell.addShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + "/index/hello" + path);
    }

    @RequestMapping({"/tomcat/filter"})
    public ResponseEntity<?> tomcatFilterMemShell() throws Exception {
        String path = FilterShell.addFilterShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + path);
    }

    @RequestMapping({"/tomcat/listener"})
    public ResponseEntity<?> tomcatListenerMemShell() throws Exception {
        String path = ListenerShell.addListenerShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + "/index/hello" + path);
    }

    @RequestMapping({"/weblogic/filter"})
    public ResponseEntity<?> webLogicFilterMemShell() throws Exception {
        String path = cn.rasp.vuln.memshell.weblogic.FilterShell.addFilterShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + path);
    }

    @RequestMapping({"/weblogic/listener"})
    public ResponseEntity<?> webLogicListenerMemShell() throws Exception {
        String path = cn.rasp.vuln.memshell.weblogic.ListenerShell.addListenerShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + "/index/hello" + path);
    }

    @RequestMapping({"/websphere/filter"})
    public ResponseEntity<?> websphereFilterMemShell() throws Exception {
        new cn.rasp.vuln.memshell.websphere.FilterShell();
        cn.rasp.vuln.memshell.websphere.FilterShell.addFilter();
        return ResponseEntity.ok(cn.rasp.vuln.memshell.websphere.FilterShell.getClassName());
    }

    @RequestMapping({"/websphere/listener"})
    public ResponseEntity<?> websphereListenerMemShell() throws Exception {
        new cn.rasp.vuln.memshell.websphere.ListenerShell();
        cn.rasp.vuln.memshell.websphere.ListenerShell.addListener();
        return ResponseEntity.ok(cn.rasp.vuln.memshell.websphere.ListenerShell.getClassName());
    }

    @RequestMapping({"/resin/filter"})
    public ResponseEntity<?> resinFilterMemShell() throws Exception {
        String path = cn.rasp.vuln.memshell.resin.FilterShell.addFilterShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + path);
    }

    @RequestMapping({"/resin/listener"})
    public ResponseEntity<?> resinListenerMemShell() throws Exception {
        String path = cn.rasp.vuln.memshell.resin.ListenerShell.addListenerShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + "/index/hello" + path);
    }

    @RequestMapping({"/jetty/filter"})
    public ResponseEntity<?> jettyFilterMemShell() throws Exception {
        String path = cn.rasp.vuln.memshell.jetty.FilterShell.addFilterShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + path);
    }

    @RequestMapping({"/jetty/listener"})
    public ResponseEntity<?> jettyListenerMemShell() throws Exception {
        String path = cn.rasp.vuln.memshell.jetty.ListenerShell.addListenerShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + "/index/hello" + path);
    }

    @RequestMapping({"/undertow/filter"})
    public ResponseEntity<?> undertowFilterMemShell() throws Exception {
        String path = cn.rasp.vuln.memshell.undertow.FilterShell.addFilterShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + path);
    }

    @RequestMapping({"/undertow/listener"})
    public ResponseEntity<?> undertowListenerMemShell() throws Exception {
        String path = cn.rasp.vuln.memshell.undertow.ListenerShell.addListenerShell();
        return ResponseEntity.ok(HttpServletRequestUtils.getAppURL() + "/index/hello" + path);
    }

    @RequestMapping({"/glassfish/filter"})
    public ResponseEntity<?> glassfishFilterMemShell() throws Exception {
        cn.rasp.vuln.memshell.glassfish.FilterShell filterShell = new cn.rasp.vuln.memshell.glassfish.FilterShell();
        return ResponseEntity.ok(filterShell.getClassName());
    }

    @RequestMapping({"/glassfish/listener"})
    public ResponseEntity<?> glassfishListenerMemShell() throws Exception {
        cn.rasp.vuln.memshell.glassfish.ListenerShell listenerShell = new cn.rasp.vuln.memshell.glassfish.ListenerShell();
        return ResponseEntity.ok(listenerShell.getClassName());
    }
}
