package cn.rasp.vuln.controller;

import cn.rasp.vuln.cmd.CommandExecution;
import cn.rasp.vuln.controller.common.AbstractFullController;
import cn.rasp.vuln.entity.AttackPayload;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: CmdController.java */
@RequestMapping({"/cmd/unix_process"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/UnixProcessController.class */
class UnixProcessController extends AbstractFullController {
    UnixProcessController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(CommandExecution.unixProcess(payload.getPayload()));
    }
}
