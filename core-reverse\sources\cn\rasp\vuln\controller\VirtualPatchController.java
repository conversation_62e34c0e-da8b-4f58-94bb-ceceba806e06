package cn.rasp.vuln.controller;

import cn.rasp.vuln.cmd.CommandExecution;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/virtual_patch/"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/VirtualPatchController.class */
public class VirtualPatchController {
    @RequestMapping({"/shell"})
    public ResponseEntity<String> getShell(String cmd) throws Exception {
        return ResponseEntity.ok(CommandExecution.processBuild(cmd));
    }
}
