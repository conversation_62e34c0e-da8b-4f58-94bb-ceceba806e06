package cn.rasp.vuln.controller;

import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.weakpass.WeakPass;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/weak_passwd"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/WeakPassController.class */
public class WeakPassController {
    @PostMapping({"jedis"})
    public ResponseEntity<?> redis(AttackPayload payload) {
        return ResponseEntity.ok(WeakPass.jedis(payload.getPayload()));
    }

    @PostMapping({"lettuce"})
    public ResponseEntity<?> lettuce(AttackPayload payload) {
        return ResponseEntity.ok(WeakPass.lettuce(payload.getPayload()));
    }

    @PostMapping({"redisson"})
    public ResponseEntity<?> redisson(AttackPayload payload) {
        WeakPass.redisson(payload.getPayload());
        return ResponseEntity.ok("success");
    }

    @PostMapping({"mongodb"})
    public ResponseEntity<?> mongodb(AttackPayload payload) {
        WeakPass.mongodb(payload.getPayload());
        return ResponseEntity.ok("success");
    }
}
