package cn.rasp.vuln.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/whitelist"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/WhitelistController.class */
public class WhitelistController {
    @GetMapping({"/url"})
    public ResponseEntity<?> url(String xss) {
        return ResponseEntity.ok(xss);
    }
}
