package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractCommonController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.xpath.XPathSearch;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/xpath/"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/XPathController.class */
public class XPathController extends AbstractCommonController {
    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        String username = payload.getArg1();
        String password = payload.getArg2();
        return ResponseEntity.ok(XPathSearch.search(username, password));
    }
}
