package cn.rasp.vuln.controller.common;

import cn.rasp.vuln.annotation.AttackCookie;
import cn.rasp.vuln.annotation.AttackHeader;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.utils.AesCrypto;
import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.MatrixVariable;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/common/AbstractFullController.class */
public abstract class AbstractFullController implements CommonController {
    private ResponseEntity<?> attack(AttackPayload payload) throws Exception {
        if (payload.getPayload() == null && payload.getArg1() == null) {
            throw new RuntimeException("请传入参数 payload 或 arg1, arg2");
        }
        AttackPayload attackPayload = new AttackPayload();
        if (payload.getPayload() != null) {
            try {
                attackPayload.setPayload(AesCrypto.decrypt(payload.getPayload()));
            } catch (Exception e) {
                attackPayload.setPayload(payload.getPayload());
            }
        }
        if (payload.getArg1() != null) {
            try {
                attackPayload.setArg1(AesCrypto.decrypt(payload.getArg1()));
            } catch (Exception e2) {
                attackPayload.setArg1(payload.getArg1());
            }
        }
        if (payload.getArg2() != null) {
            try {
                attackPayload.setArg2(AesCrypto.decrypt(payload.getArg2()));
            } catch (Exception e3) {
                attackPayload.setArg2(payload.getArg2());
            }
        }
        if (payload.getArg3() != null) {
            try {
                attackPayload.setArg3(AesCrypto.decrypt(payload.getArg3()));
            } catch (Exception e4) {
                attackPayload.setArg3(payload.getArg3());
            }
        }
        return execute(attackPayload);
    }

    @GetMapping({"/getParam"})
    public ResponseEntity<?> getParam(AttackPayload payload) throws Exception {
        return attack(payload);
    }

    @GetMapping({"/getCookie"})
    public ResponseEntity<?> getCookie(@AttackCookie AttackPayload payload) throws Exception {
        return attack(payload);
    }

    @GetMapping({"/getHeader"})
    public ResponseEntity<?> getHeader(@AttackHeader AttackPayload payload) throws Exception {
        return attack(payload);
    }

    @GetMapping({"/getPath/{payload}"})
    public ResponseEntity<?> getPathPayload(@PathVariable String payload) throws Exception {
        AttackPayload attackPayload = new AttackPayload();
        attackPayload.setPayload(payload);
        return attack(attackPayload);
    }

    @GetMapping({"/{getMatrix}"})
    public ResponseEntity<?> getMatrix(@MatrixVariable(pathVar = "getMatrix") MultiValueMap<String, String> payload) throws Exception {
        AttackPayload attackPayload = new AttackPayload();
        List<String> payloads = (List) payload.get("payload");
        attackPayload.setPayload(payloads.get(0));
        return attack(attackPayload);
    }

    @PostMapping({"/postParam"})
    public ResponseEntity<?> postParam(AttackPayload payload) throws Exception {
        return attack(payload);
    }

    @PostMapping({"/postBody"})
    public ResponseEntity<?> postBody(AttackPayload payload) throws Exception {
        return attack(payload);
    }

    @PostMapping(value = {"/postXml"}, consumes = {"application/xml"})
    public ResponseEntity<?> postXml(@RequestBody AttackPayload payload) throws Exception {
        return attack(payload);
    }

    @PostMapping(value = {"/postJson"}, consumes = {"application/json"})
    public ResponseEntity<?> postJson(@RequestBody AttackPayload payload) throws Exception {
        return attack(payload);
    }
}
