package cn.rasp.vuln.controller.common;

import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.utils.AesCrypto;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;

/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/common/AbstractPostBodyController.class */
public abstract class AbstractPostBodyController implements CommonController {
    private ResponseEntity<?> attack(AttackPayload payload) throws Exception {
        if (payload.getPayload() == null) {
            throw new RuntimeException("please sent payload value");
        }
        AttackPayload attackPayload = new AttackPayload();
        if (payload.getPayload() != null) {
            try {
                attackPayload.setPayload(AesCrypto.decrypt(payload.getPayload()));
            } catch (Exception e) {
                attackPayload.setPayload(payload.getPayload());
            }
        }
        return execute(attackPayload);
    }

    @PostMapping({"/postBody"})
    public ResponseEntity<?> postBody(AttackPayload payload) throws Exception {
        return attack(payload);
    }
}
