package cn.rasp.vuln.controller.file;

import cn.rasp.vuln.controller.common.AbstractCommonController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.file.FileAccess;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: NIOFileController.java */
@RequestMapping({"/file/nio/file_read_all_lines"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/file/NIOFileReadAllLinesController.class */
class NIOFileReadAllLinesController extends AbstractCommonController {
    NIOFileReadAllLinesController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(FileAccess.NIOFile.readAllLines(payload.getPayload()));
    }
}
