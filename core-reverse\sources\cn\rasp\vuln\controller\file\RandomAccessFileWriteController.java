package cn.rasp.vuln.controller.file;

import cn.rasp.vuln.controller.common.AbstractCommonController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.file.FileAccess;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: RandomAccessFileController.java */
@RequestMapping({"/file/random_access_file/write"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/file/RandomAccessFileWriteController.class */
class RandomAccessFileWriteController extends AbstractCommonController {
    RandomAccessFileWriteController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        FileAccess.RandomFile.write(payload.getArg1(), payload.getArg2());
        return ResponseEntity.ok(payload.getArg1());
    }
}
