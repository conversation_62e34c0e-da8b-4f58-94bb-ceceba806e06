package cn.rasp.vuln.entity;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import javax.xml.bind.annotation.XmlRootElement;

@JacksonXmlRootElement(localName = "root")
@XmlRootElement(name = "root")
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/entity/AttackPayload.class */
public class AttackPayload {
    private String payload;
    private Boolean encrypt;
    private String arg1;
    private String arg2;
    private String arg3;

    public void setPayload(String payload) {
        this.payload = payload;
    }

    public void setEncrypt(Boolean encrypt) {
        this.encrypt = encrypt;
    }

    public void setArg1(String arg1) {
        this.arg1 = arg1;
    }

    public void setArg2(String arg2) {
        this.arg2 = arg2;
    }

    public void setArg3(String arg3) {
        this.arg3 = arg3;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof AttackPayload)) {
            return false;
        }
        AttackPayload other = (AttackPayload) o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$encrypt = getEncrypt();
        Object other$encrypt = other.getEncrypt();
        if (this$encrypt == null) {
            if (other$encrypt != null) {
                return false;
            }
        } else if (!this$encrypt.equals(other$encrypt)) {
            return false;
        }
        Object this$payload = getPayload();
        Object other$payload = other.getPayload();
        if (this$payload == null) {
            if (other$payload != null) {
                return false;
            }
        } else if (!this$payload.equals(other$payload)) {
            return false;
        }
        Object this$arg1 = getArg1();
        Object other$arg1 = other.getArg1();
        if (this$arg1 == null) {
            if (other$arg1 != null) {
                return false;
            }
        } else if (!this$arg1.equals(other$arg1)) {
            return false;
        }
        Object this$arg2 = getArg2();
        Object other$arg2 = other.getArg2();
        if (this$arg2 == null) {
            if (other$arg2 != null) {
                return false;
            }
        } else if (!this$arg2.equals(other$arg2)) {
            return false;
        }
        Object this$arg3 = getArg3();
        Object other$arg3 = other.getArg3();
        return this$arg3 == null ? other$arg3 == null : this$arg3.equals(other$arg3);
    }

    protected boolean canEqual(Object other) {
        return other instanceof AttackPayload;
    }

    public int hashCode() {
        Object $encrypt = getEncrypt();
        int result = (1 * 59) + ($encrypt == null ? 43 : $encrypt.hashCode());
        Object $payload = getPayload();
        int result2 = (result * 59) + ($payload == null ? 43 : $payload.hashCode());
        Object $arg1 = getArg1();
        int result3 = (result2 * 59) + ($arg1 == null ? 43 : $arg1.hashCode());
        Object $arg2 = getArg2();
        int result4 = (result3 * 59) + ($arg2 == null ? 43 : $arg2.hashCode());
        Object $arg3 = getArg3();
        return (result4 * 59) + ($arg3 == null ? 43 : $arg3.hashCode());
    }

    public String toString() {
        return "AttackPayload(payload=" + getPayload() + ", encrypt=" + getEncrypt() + ", arg1=" + getArg1() + ", arg2=" + getArg2() + ", arg3=" + getArg3() + ")";
    }

    public String getPayload() {
        return this.payload;
    }

    public Boolean getEncrypt() {
        return this.encrypt;
    }

    public String getArg1() {
        return this.arg1;
    }

    public String getArg2() {
        return this.arg2;
    }

    public String getArg3() {
        return this.arg3;
    }
}
