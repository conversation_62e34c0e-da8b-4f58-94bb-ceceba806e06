package cn.rasp.vuln.entity;

/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/entity/SysUser.class */
public class SysUser {
    private Long userId;
    private String username;
    private String password;
    private String email;
    private String userAvatar;
    private String registerTime;
    private Object notes;

    public Long getUserId() {
        return this.userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUserAvatar() {
        return this.userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public String getRegisterTime() {
        return this.registerTime;
    }

    public void setRegisterTime(String registerTime) {
        this.registerTime = registerTime;
    }

    public Object getNotes() {
        return this.notes;
    }

    public void setNotes(Object notes) {
        this.notes = notes;
    }
}
