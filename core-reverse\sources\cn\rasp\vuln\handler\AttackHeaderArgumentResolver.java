package cn.rasp.vuln.handler;

import cn.rasp.vuln.annotation.AttackHeader;
import cn.rasp.vuln.entity.AttackPayload;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/handler/AttackHeaderArgumentResolver.class */
public class AttackHeaderArgumentResolver implements HandlerMethodArgumentResolver {
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.hasParameterAnnotation(AttackHeader.class);
    }

    private String getHeader(String argName, NativeWebRequest nativeWebRequest) {
        String[] headerValues = nativeWebRequest.getHeaderValues(argName);
        if (headerValues == null || headerValues.length <= 0) {
            return null;
        }
        return headerValues[0];
    }

    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        AttackPayload result = new AttackPayload();
        result.setPayload(getHeader("payload", nativeWebRequest));
        result.setArg1(getHeader("arg1", nativeWebRequest));
        result.setArg2(getHeader("arg2", nativeWebRequest));
        result.setArg3(getHeader("arg3", nativeWebRequest));
        result.setEncrypt(Boolean.valueOf(getHeader("encrypt", nativeWebRequest)));
        return result;
    }
}
