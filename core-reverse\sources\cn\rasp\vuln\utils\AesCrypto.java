package cn.rasp.vuln.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Base64;

/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/utils/AesCrypto.class */
public class AesCrypto {
    private static final String ALGORITHM = "AES";
    private static final String KEY = "AES_ENCRYPT_RASP";

    public static String encrypt(String plaintext) throws Exception {
        byte[] keyBytes = KEY.getBytes();
        SecretKeySpec key = new SecretKeySpec(keyBytes, ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(1, key);
        byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes());
        return Base64.encodeBase64String(encryptedBytes);
    }

    public static String decrypt(String ciphertext) throws Exception {
        byte[] keyBytes = KEY.getBytes();
        SecretKeySpec key = new SecretKeySpec(keyBytes, ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(2, key);
        byte[] decryptedBytes = cipher.doFinal(Base64.decodeBase64(ciphertext.replaceAll("\\s", "+")));
        return new String(decryptedBytes);
    }
}
