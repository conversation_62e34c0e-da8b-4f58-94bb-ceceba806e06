package cn.rasp.vuln.utils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/utils/ReflectionUtils.class */
public final class ReflectionUtils {
    public static final Class<?>[] EMPTY_ARGS = new Class[0];
    public static final Class<?>[] BOOLEAN_CLASS_ARG = {Boolean.TYPE};
    public static final Class<?>[] STRING_CLASS_ARG = {String.class};
    public static final Class<?>[] STRING_STRING_CLASS_ARG = {String.class, String.class};
    public static final Class<?>[] INT_STRING_CLASS_ARG = {Integer.TYPE, String.class};
    public static final Class<?>[] STRING_OBJECT_CLASS_ARG = {String.class, Object.class};
    public static final Class<?>[] INT_CLASS_ARG = {Integer.TYPE};
    public static final Class<?>[] BYTE_ARRAY_CLASS_ARG = {byte[].class};
    public static final Class<?>[] STREAM_CLASS_ARG = {byte[].class, Integer.TYPE, Integer.TYPE};
    private static final Map<Integer, Method> CACHE_CLASS_METHOD_MAP = new ConcurrentHashMap();
    private static final Map<Integer, Field> CACHE_CLASS_FIELD_MAP = new ConcurrentHashMap();

    public static void clear() {
        CACHE_CLASS_METHOD_MAP.clear();
        CACHE_CLASS_FIELD_MAP.clear();
    }

    public static String methodToString(String className, String method, Class<?>... classes) {
        StringBuilder sb = new StringBuilder();
        if (className != null) {
            sb.append(className).append('.');
        }
        return sb.append(method).append(methodToString(classes)).toString();
    }

    public static String methodToString(Class<?>... classes) {
        StringBuilder sb = new StringBuilder("(");
        for (int i = 0; i < classes.length; i++) {
            if (i > 0) {
                sb.append(',');
            }
            sb.append(classes[i].getName());
        }
        return sb.append(')').toString();
    }

    public static String getMethodHashcode(Object className, String method) {
        StringBuilder sb = new StringBuilder();
        if (className != null) {
            sb.append(className);
        }
        if (method != null) {
            if (sb.length() > 0) {
                sb.append('#');
            }
            sb.append(method);
        }
        return sb.toString();
    }

    public static int getMethodHashcode(Object className, String method, Class<?>... typeClasses) {
        StringBuilder sb = new StringBuilder();
        sb.append(getMethodHashcode(className, method));
        if (typeClasses.length > 0) {
            sb.append('(');
            for (int i = 0; i < typeClasses.length; i++) {
                if (i > 0) {
                    sb.append(',');
                }
                sb.append(typeClasses[i].getName());
            }
            sb.append(')');
        }
        return sb.toString().hashCode();
    }

    public static int getFieldHashcode(Object className, String field) {
        int hashcode = 0;
        if (className != null) {
            hashcode = className.hashCode();
        }
        if (field != null) {
            hashcode += field.hashCode();
        }
        return hashcode;
    }

    public static Method getMethod(Class<?> clazz, String name, Class<?>... argTypes) throws NoSuchMethodException {
        int hashCode = getMethodHashcode(clazz, name, argTypes);
        Method method = CACHE_CLASS_METHOD_MAP.get(Integer.valueOf(hashCode));
        if (method != null) {
            return method;
        }
        while (clazz != Object.class) {
            try {
                method = clazz.getDeclaredMethod(name, argTypes);
                break;
            } catch (NoSuchMethodException e) {
                clazz = clazz.getSuperclass();
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(name);
        }
        method.setAccessible(true);
        CACHE_CLASS_METHOD_MAP.put(Integer.valueOf(hashCode), method);
        return method;
    }

    public static Object invokeMethod(Object instance, String name, Class<?>[] argTypes, Object... args) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        Method method = getMethod(instance.getClass(), name, argTypes);
        method.setAccessible(true);
        return method.invoke(instance, args);
    }

    public static Object invokeMethod(Object object, Class<?> clazz, String methodName, Class[] paramTypes, Object... parameters) {
        try {
            Method method = clazz.getMethod(methodName, paramTypes);
            if (!method.isAccessible()) {
                method.setAccessible(true);
            }
            return method.invoke(object, parameters);
        } catch (Exception e) {
            String str = "Reflection call " + methodName + " failed: " + e.getMessage();
            if (clazz != null) {
                String str2 = "Reflection call " + clazz.getName() + "." + methodName + " failed: " + e.getMessage();
                return null;
            }
            return null;
        }
    }

    public static <T> T invokeProxyMethod(Object obj, String str) {
        return (T) invokeProxyMethod(obj, str, EMPTY_ARGS, new Object[0]);
    }

    public static <T> T invokeProxyMethod(Object obj, String str, Class<?>[] clsArr, Object... objArr) {
        return (T) invokeMethodProxy(obj, str, clsArr, objArr);
    }

    public static Field getField(Class<?> clazz, String name) throws NoSuchFieldException {
        int hashCode = getFieldHashcode(clazz, name);
        Field field = CACHE_CLASS_FIELD_MAP.get(Integer.valueOf(hashCode));
        if (field != null) {
            return field;
        }
        while (clazz != Object.class) {
            try {
                field = clazz.getDeclaredField(name);
                break;
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        if (field == null) {
            throw new NoSuchFieldException(name);
        }
        field.setAccessible(true);
        CACHE_CLASS_FIELD_MAP.put(Integer.valueOf(hashCode), field);
        return field;
    }

    public static Object invokeField(Object instance, String name) throws NoSuchFieldException, IllegalAccessException {
        Field field = getField(instance.getClass(), name);
        return field.get(instance);
    }

    public static <T> T invokeMethodProxy(Object obj, String str, Class<?>[] clsArr, Object... objArr) {
        try {
            return (T) invokeMethod(obj, str, clsArr, objArr);
        } catch (Exception e) {
            return null;
        }
    }

    public static <T> T invokeFieldProxy(Object obj, String str) {
        try {
            return (T) invokeField(obj, str);
        } catch (Exception e) {
            return null;
        }
    }

    public static Method unCaughtGetClassDeclaredJavaMethod(Class<?> clazz, String name, Class<?>... parameterClassArray) {
        try {
            return clazz.getDeclaredMethod(name, parameterClassArray);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T unCaughtInvokeMethod(Method method, Object obj, Object... objArr) {
        RuntimeException runtimeException;
        boolean isAccessible = method.isAccessible();
        try {
            try {
                method.setAccessible(true);
                T t = (T) method.invoke(obj, objArr);
                method.setAccessible(isAccessible);
                return t;
            } finally {
            }
        } catch (Throwable th) {
            method.setAccessible(isAccessible);
            throw th;
        }
    }

    public static Field unCaughtGetClassDeclaredJavaField(Class<?> clazz, String name) {
        try {
            return clazz.getDeclaredField(name);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T unCaughtGetClassDeclaredJavaFieldValue(Class<?> cls, String str, Object obj) {
        Field unCaughtGetClassDeclaredJavaField = unCaughtGetClassDeclaredJavaField(cls, str);
        boolean isAccessible = unCaughtGetClassDeclaredJavaField.isAccessible();
        try {
            try {
                unCaughtGetClassDeclaredJavaField.setAccessible(true);
                T t = (T) unCaughtGetClassDeclaredJavaField.get(obj);
                unCaughtGetClassDeclaredJavaField.setAccessible(isAccessible);
                return t;
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        } catch (Throwable th) {
            unCaughtGetClassDeclaredJavaField.setAccessible(isAccessible);
            throw th;
        }
    }

    public static void unCaughtSetClassDeclaredJavaFieldValue(Class<?> clazz, String name, Object target, Object value) {
        Field field = unCaughtGetClassDeclaredJavaField(clazz, name);
        boolean isAccessible = field.isAccessible();
        try {
            try {
                field.setAccessible(true);
                field.set(target, value);
                field.setAccessible(isAccessible);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        } catch (Throwable th) {
            field.setAccessible(isAccessible);
            throw th;
        }
    }
}
