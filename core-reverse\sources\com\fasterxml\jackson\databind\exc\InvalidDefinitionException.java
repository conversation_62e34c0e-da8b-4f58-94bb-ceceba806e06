package com.fasterxml.jackson.databind.exc;

import org.springframework.http.converter.HttpMessageConversionException;

/* loaded from: vuln-core-3.0.3.jar:com/fasterxml/jackson/databind/exc/InvalidDefinitionException.class */
public class InvalidDefinitionException extends HttpMessageConversionException {
    public InvalidDefinitionException(String msg, Throwable cause) {
        super(msg, cause);
    }

    public String getType() {
        return "UNKNOWN";
    }
}
