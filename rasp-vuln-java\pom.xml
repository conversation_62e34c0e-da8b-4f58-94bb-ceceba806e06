<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.rasp.vuln</groupId>
    <artifactId>cloudrasp-vuln-java</artifactId>
    <version>3.0.3</version>
    <packaging>pom</packaging>

    <name>CloudRASP Vulnerability Java</name>
    <description>RASP vulnerability testing platform</description>

    <modules>
        <module>vuln-common</module>
        <module>vuln-core</module>
        <module>vuln-web</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>8</java.version>

        <!-- Spring Boot -->
        <spring-boot.version>2.7.18</spring-boot.version>
        
        <!-- Spring Framework -->
        <springframework.version>5.3.31</springframework.version>
        <spring-data-jpa.version>2.7.18</spring-data-jpa.version>

        <!-- Database -->
        <mysql-connector-java.version>8.0.33</mysql-connector-java.version>
        <h2.version>2.1.214</h2.version>
        <sqlite.version>********</sqlite.version>
        <druid.version>1.2.18</druid.version>

        <!-- JSON -->
        <jackson.version>2.15.2</jackson.version>
        <fastjson.version>1.2.83</fastjson.version>
        <gson.version>2.10.1</gson.version>

        <!-- XML -->
        <snakeyaml.version>1.33</snakeyaml.version>
        <xstream.version>1.4.20</xstream.version>
        <dom4j.version>1.6.1</dom4j.version>
        <xerces.version>2.12.2</xerces.version>
        <xalan.version>2.7.3</xalan.version>

        <!-- Expression Languages -->
        <ognl.version>3.3.4</ognl.version>
        <mvel2.version>2.4.14.Final</mvel2.version>
        <groovy.version>3.0.17</groovy.version>
        <jexl.version>2.1.1</jexl.version>
        <jexl3.version>3.3</jexl3.version>
        <el.version>2.2.6</el.version>
        <rhino.version>1.7.14</rhino.version>

        <!-- Template Engines -->
        <velocity.version>1.7</velocity.version>
        <freemarker.version>2.3.32</freemarker.version>
        <jinjava.version>2.7.1</jinjava.version>

        <!-- Serialization -->
        <hessian.version>4.0.66</hessian.version>
        <hessian-lite.version>3.2.13</hessian-lite.version>
        <kryo.version>5.4.0</kryo.version>
        <castor.version>1.4.1</castor.version>

        <!-- HTTP Clients -->
        <httpclient.version>4.5.14</httpclient.version>
        <okhttp3.version>4.11.0</okhttp3.version>
        <commons-httpclient.version>3.1</commons-httpclient.version>
        <netty.version>4.1.94.Final</netty.version>

        <!-- Commons -->
        <commons-io.version>2.11.0</commons-io.version>
        <commons-codec.version>1.15</commons-codec.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <commons-fileupload.version>1.5</commons-fileupload.version>
        <commons-configuration.version>1.10</commons-configuration.version>

        <!-- Logging -->
        <slf4j.version>1.7.36</slf4j.version>
        <log4j.version>2.20.0</log4j.version>

        <!-- Other -->
        <aspectjweaver.version>1.9.19</aspectjweaver.version>
        <javassist.version>3.29.2-GA</javassist.version>
        <jsoup.version>1.16.1</jsoup.version>
        <easyexcel.version>3.3.2</easyexcel.version>
        <shiro.version>1.11.0</shiro.version>

        <!-- Database Drivers -->
        <postgresql.version>42.6.0</postgresql.version>
        <oracle.version>21.9.0.0</oracle.version>
        <mssql-jdbc.version>12.2.0.jre8</mssql-jdbc.version>
        <db2.version>11.5.8.0</db2.version>
        <derby.version>10.16.1.1</derby.version>
        <kingbase-jdbc.version>8.6.0</kingbase-jdbc.version>

        <!-- Cache -->
        <redis.version>4.4.3</redis.version>
        <mongodb-driver.version>3.12.14</mongodb-driver.version>

        <!-- Build Plugins -->
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>

        <!-- Other Libraries -->
        <c3p0.version>0.9.5.5</c3p0.version>
        <blazeds.version>4.7.3</blazeds.version>
        <jyaml.version>1.3</jyaml.version>
        <yamlbeans.version>1.15</yamlbeans.version>
        <json-io.version>4.14.1</json-io.version>
        <red5io.versoin>1.0.10-M8</red5io.versoin>
        <jxpath.version>1.3</jxpath.version>
        <jom.version>2.0.6</jom.version>
        <modeshape.version>5.4.1.Final</modeshape.version>
        <javaweb.version>1.2.0</javaweb.version>
        <javax.persistence-api.version>2.2</javax.persistence-api.version>
        <jakarta.persistence-api.version>3.1.0</jakarta.persistence-api.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <skipTests>true</skipTests>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
