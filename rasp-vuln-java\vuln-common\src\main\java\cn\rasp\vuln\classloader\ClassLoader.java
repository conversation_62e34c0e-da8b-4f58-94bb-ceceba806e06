package cn.rasp.vuln.classloader;

import java.lang.reflect.Method;
import org.apache.bcel.classfile.Utility;
import org.apache.commons.codec.binary.Base64;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/classloader/ClassLoader.class */
public class ClassLoader {
    public static String defineClass(String base64) {
        try {
            java.lang.ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            byte[] bytes = Base64.decodeBase64(base64);
            Method defineClass = java.lang.ClassLoader.class.getDeclaredMethod("defineClass", byte[].class, Integer.TYPE, Integer.TYPE);
            defineClass.setAccessible(true);
            Class clazz = (Class) defineClass.invoke(classLoader, bytes, 0, Integer.valueOf(bytes.length));
            return clazz.getName();
        } catch (Throwable throwable) {
            throw new RuntimeException(throwable);
        }
    }

    public static String bcel(String base64) {
        try {
            org.apache.bcel.util.ClassLoader classLoader = new org.apache.bcel.util.ClassLoader();
            byte[] bytes = Base64.decodeBase64(base64);
            String className = "$$BCEL$$" + Utility.encode(bytes, true);
            Class<?> clazz = Class.forName(className, true, classLoader);
            return clazz.getName();
        } catch (Throwable throwable) {
            throw new RuntimeException(throwable);
        }
    }
}
