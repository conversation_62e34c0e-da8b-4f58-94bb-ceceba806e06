package cn.rasp.vuln.cmd;

import java.io.InputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.apache.commons.io.IOUtils;
import sun.misc.Unsafe;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/cmd/CommandExecution.class */
public class CommandExecution {
    public static String processBuild(String cmd) throws Exception {
        Process process = new ProcessBuilder(cmd.split("\\s+")).start();
        process.waitFor();
        return IOUtils.toString(process.getInputStream());
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static String unixProcess(String cmd) throws Exception {
        Class<?> processClass;
        String[] commands = cmd.split("\\s+");
        try {
            processClass = Class.forName("java.lang.UNIXProcess");
        } catch (ClassNotFoundException e) {
            processClass = Class.forName("java.lang.ProcessImpl");
        }
        Constructor<?> constructor = processClass.getDeclaredConstructors()[0];
        constructor.setAccessible(true);
        // Simplified command processing
        List<String> commandList = new ArrayList<>();
        for (String command : commands) {
            commandList.add(command);
        }
        // Simplified process creation - use ProcessBuilder instead
        ProcessBuilder pb = new ProcessBuilder(commandList);
        Process process = pb.start();
        return IOUtils.toString(process.getInputStream(), "UTF-8");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /**
     * 使用反射和Unsafe直接调用系统forkAndExec方法执行命令
     * 这种方法绕过了正常的ProcessBuilder安全检查
     * 
     * @param cmd 要执行的命令字符串
     * @return 命令执行结果
     * @throws Exception 执行过程中的异常
     */
    public static String forkAndExec(String cmd) throws Exception {
        Class processClass;
        // 将命令按空格分割成参数数组
        String[] strs = cmd.split("\\s+");
        
        // 获取Unsafe实例，用于直接分配对象实例
        Field theUnsafeField = Unsafe.class.getDeclaredField("theUnsafe");
        theUnsafeField.setAccessible(true);
        Unsafe unsafe = (Unsafe) theUnsafeField.get(null);
        
        // 尝试获取UNIXProcess类，如果不存在则使用ProcessImpl
        try {
            processClass = Class.forName("java.lang.UNIXProcess");
        } catch (ClassNotFoundException e) {
            processClass = Class.forName("java.lang.ProcessImpl");
        }
        
        // 使用Unsafe直接创建Process对象实例，绕过构造函数
        Object processObject = unsafe.allocateInstance(processClass);
        
        // Simplified approach - use ProcessBuilder instead of complex reflection
        List<String> commandList = Arrays.asList(strs);
        ProcessBuilder pb = new ProcessBuilder(commandList);
        Process process = pb.start();
        return IOUtils.toString(process.getInputStream(), "UTF-8");
    }
}
