package cn.rasp.vuln.engine;

import javax.script.ScriptEngineManager;
import org.mozilla.javascript.Context;
import org.mozilla.javascript.Function;
import org.mozilla.javascript.ScriptableObject;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/engine/ScriptEngine.class */
public class ScriptEngine {
    public static String scriptEngineEval(String exp) throws Exception {
        Object eval = new ScriptEngineManager((ClassLoader) null).getEngineByName("js").eval(exp);
        return eval.toString();
    }

    public static Object rhinoEval(String exp) throws Exception {
        Context ct = Context.enter();
        ScriptableObject initStandardObjects = ct.initStandardObjects();
        ct.evaluateString(initStandardObjects, exp, (String) null, 1, (Object) null);
        Function fct = (Function) initStandardObjects.get("test", initStandardObjects);
        Object result = fct.call(ct, initStandardObjects, initStandardObjects, (Object[]) null);
        return Context.jsToJava(result, String.class);
    }
}
