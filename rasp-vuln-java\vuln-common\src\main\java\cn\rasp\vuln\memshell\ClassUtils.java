package cn.rasp.vuln.memshell;

import java.lang.reflect.Method;
import org.apache.commons.codec.binary.Base64;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/ClassUtils.class */
public class ClassUtils {
    public static Object getInstance(String base64, String className) {
        Object instance = null;
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        try {
            instance = classLoader.loadClass(className).newInstance();
        } catch (Exception e) {
            try {
                byte[] bytes = Base64.decodeBase64(base64);
                Method defineClass = ClassLoader.class.getDeclaredMethod("defineClass", byte[].class, Integer.TYPE, Integer.TYPE);
                defineClass.setAccessible(true);
                Class clazz = (Class) defineClass.invoke(classLoader, bytes, 0, Integer.valueOf(bytes.length));
                instance = clazz.newInstance();
            } catch (Throwable th) {
            }
        }
        return instance;
    }

    public static Object getInstance(ClassLoader classLoader, byte[] base64, String className) throws Exception {
        if (classLoader == null) {
            return null;
        }
        try {
            return classLoader.loadClass(className).newInstance();
        } catch (Exception e) {
            Method defineClass = ClassLoader.class.getDeclaredMethod("defineClass", byte[].class, Integer.TYPE, Integer.TYPE);
            defineClass.setAccessible(true);
            Class clazz = (Class) defineClass.invoke(classLoader, base64, 0, Integer.valueOf(base64.length));
            return clazz.newInstance();
        }
    }
}
