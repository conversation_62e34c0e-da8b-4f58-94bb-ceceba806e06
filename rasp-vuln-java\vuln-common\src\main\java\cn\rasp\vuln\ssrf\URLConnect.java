package cn.rasp.vuln.ssrf;

import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.http.DefaultFullHttpRequest;
import io.netty.handler.codec.http.HttpContent;
import io.netty.handler.codec.http.HttpMethod;
import io.netty.handler.codec.http.HttpRequestEncoder;
import io.netty.handler.codec.http.HttpResponse;
import io.netty.handler.codec.http.HttpResponseDecoder;
import io.netty.handler.codec.http.HttpVersion;
import io.netty.util.CharsetUtil;
import java.net.URI;
import java.net.URL;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/ssrf/URLConnect.class */
public class URLConnect {
    public static byte[] urlConnect(String url) throws Exception {
        return IOUtils.toByteArray(new URL(url).openConnection().getInputStream());
    }

    public static byte[] okHttp3(String url) throws Exception {
        Request build = new Request.Builder().url(url).get().build();
        return new OkHttpClient().newCall(build).execute().body().bytes();
    }

    public static void netty(String ip, String url) throws Exception {
        String[] split = ip.split(":");
        String host = split[0];
        int port = Integer.parseInt(split[1]);
        NioEventLoopGroup nioEventLoopGroup = new NioEventLoopGroup();
        try {
            Bootstrap b = new Bootstrap();
            b.group(nioEventLoopGroup);
            b.channel(NioSocketChannel.class);
            b.option(ChannelOption.SO_KEEPALIVE, true);
            b.handler(new ChannelInitializer<SocketChannel>() { // from class: cn.rasp.vuln.ssrf.URLConnect.1
                public void initChannel(SocketChannel ch) throws Exception {
                    ch.pipeline().addLast(new ChannelHandler[]{new HttpResponseDecoder()});
                    ch.pipeline().addLast(new ChannelHandler[]{new HttpRequestEncoder()});
                    ch.pipeline().addLast(new ChannelHandler[]{new HttpClientInboundHandler()});
                }
            });
            ChannelFuture f = b.connect(host, port).sync();
            URI uri = new URI(url);
            DefaultFullHttpRequest request = new DefaultFullHttpRequest(HttpVersion.HTTP_1_1, HttpMethod.GET, uri.toASCIIString());
            request.headers().set("Host", host);
            request.headers().set("Connection", "keep-alive");
            request.headers().set("Content-Length", Integer.valueOf(request.content().readableBytes()));
            f.channel().write(request);
            f.channel().flush();
            f.channel().closeFuture().sync();
            nioEventLoopGroup.shutdownGracefully();
        } catch (Throwable th) {
            nioEventLoopGroup.shutdownGracefully();
            throw th;
        }
    }

    /* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/ssrf/URLConnect$HttpClientInboundHandler.class */
    private static class HttpClientInboundHandler extends ChannelInboundHandlerAdapter {
        private HttpClientInboundHandler() {
        }

        public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
            if (msg instanceof HttpResponse) {
                HttpResponse response = (HttpResponse) msg;
                System.out.println("CONTENT_TYPE:" + response.headers().get("Content-Type"));
            }
            if (msg instanceof HttpContent) {
                HttpContent content = (HttpContent) msg;
                ByteBuf buf = content.content();
                System.out.println(buf.toString(CharsetUtil.UTF_8));
                buf.release();
            }
        }
    }

    public static byte[] httpClient(String url) throws Exception {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpEntity responseEntity = httpClient.execute(new HttpGet(url)).getEntity();
        byte[] bytes = IOUtils.toByteArray(responseEntity.getContent());
        httpClient.close();
        return bytes;
    }

    public static byte[] commonHttpClient(String url) throws Exception {
        HttpClient httpClient = new HttpClient();
        GetMethod getMethod = new GetMethod(url);
        httpClient.executeMethod(getMethod);
        return getMethod.getResponseBody();
    }
}
