package cn.rasp.vuln.weakpass;

import com.mongodb.MongoClient;
import com.mongodb.MongoClientURI;
import io.lettuce.core.RedisClient;
import io.lettuce.core.api.StatefulRedisConnection;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/weakpass/WeakPass.class */
public class WeakPass {
    public static String jedis(String url) {
        JedisPool jedisPool = new JedisPool(url);
        Jedis jedis = jedisPool.getResource();
        String info = jedis.info();
        jedisPool.close();
        return info;
    }

    public static String lettuce(String url) {
        RedisClient redisClient = RedisClient.create(url);
        StatefulRedisConnection<String, String> connection = redisClient.connect();
        String info = connection.sync().info();
        connection.close();
        redisClient.shutdown();
        return info;
    }

    public static void redisson(String url) {
        String[] arr = url.split("@")[0].split(":");
        String password = arr[arr.length - 1];
        Config config = new Config();
        config.useSingleServer().setAddress(url).setPassword(password);
        RedissonClient client = Redisson.create(config);
        client.getBuckets();
        client.shutdown();
    }

    public static void mongodb(String url) {
        MongoClientURI mongoClientURI = new MongoClientURI(url);
        MongoClient mongoClient = new MongoClient(mongoClientURI);
        mongoClient.close();
    }
}
