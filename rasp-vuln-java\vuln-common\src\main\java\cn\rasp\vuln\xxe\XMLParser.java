package cn.rasp.vuln.xxe;

import com.ctc.wstx.stax.WstxInputFactory;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;
import javax.xml.stream.XMLStreamReader;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.xerces.dom.DOMInputImpl;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.jdom2.Element;
import org.jdom2.input.SAXBuilder;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.bootstrap.DOMImplementationRegistry;
import org.w3c.dom.ls.DOMImplementationLS;
import org.w3c.dom.ls.LSParser;
import org.xml.sax.InputSource;
import org.xml.sax.XMLReader;
import org.xml.sax.helpers.DefaultHandler;

/* loaded from: vuln-common-3.0.3.jar:cn/rasp/vuln/xxe/XMLParser.class */
public class XMLParser {
    public static String xerces(String xml) throws Exception {
        ByteArrayInputStream bais = new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8));
        DOMImplementationRegistry registry = DOMImplementationRegistry.newInstance();
        DOMImplementationLS domImpl = (DOMImplementationLS) registry.getDOMImplementation("LS");
        LSParser builder = domImpl.createLSParser((short) 1, null);
        DOMInputImpl domInput = new DOMInputImpl();
        domInput.setByteStream(bais);
        Document doc = builder.parse(domInput);
        return doc.getDocumentElement().getFirstChild().getNodeValue();
    }

    public static String documentBuilder(String xml) throws Exception {
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        DocumentBuilder db = dbf.newDocumentBuilder();
        StringReader sr = new StringReader(xml);
        InputSource is = new InputSource(sr);
        Document document = db.parse(is);
        StringBuilder buf = new StringBuilder();
        NodeList rootNodeList = document.getChildNodes();
        for (int i = 0; i < rootNodeList.getLength(); i++) {
            Node rootNode = rootNodeList.item(i);
            NodeList child = rootNode.getChildNodes();
            for (int j = 0; j < child.getLength(); j++) {
                Node node = child.item(j);
                buf.append(String.format("%s: %s\n", node.getNodeName(), node.getTextContent()));
            }
        }
        sr.close();
        return buf.toString();
    }

    public static byte[] dom4j(String xml) throws Exception {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        SAXReader reader = new SAXReader();
        StringReader in = new StringReader(xml);
        org.dom4j.Document doc = reader.read(in);
        OutputFormat format = OutputFormat.createPrettyPrint();
        format.setEncoding("UTF-8");
        XMLWriter writer = new XMLWriter(out, format);
        writer.write(doc);
        writer.flush();
        writer.close();
        return out.toByteArray();
    }

    public static String jdom(String xml) throws Exception {
        SAXBuilder saxBuilder = new SAXBuilder();
        org.jdom2.Document document = saxBuilder.build(new StringReader(xml));
        Element rootElement = document.getRootElement();
        return rootElement.getValue();
    }

    public static void saxParser(String xml) throws Exception {
        SAXParserFactory factory = SAXParserFactory.newInstance();
        SAXParser parser = factory.newSAXParser();
        parser.parse(new InputSource(new StringReader(xml)), new DefaultHandler());
    }

    public static void xmlReader(String xml) throws Exception {
        SAXParserFactory factory = SAXParserFactory.newInstance();
        SAXParser parser = factory.newSAXParser();
        XMLReader reader = parser.getXMLReader();
        reader.setContentHandler(new DefaultHandler());
        reader.parse(new InputSource(new StringReader(xml)));
    }

    public static void wstx(String xml) throws Exception {
        XMLStreamReader reader = new WstxInputFactory().createXMLStreamReader(new StringReader(xml));
        while (reader.hasNext()) {
            reader.next();
        }
        reader.close();
    }

    public static void excel(String filePath) throws Exception {
        try {
            FileInputStream fis = new FileInputStream(filePath);
            Throwable th = null;
            try {
                XSSFWorkbook workbook = new XSSFWorkbook(fis);
                Throwable th2 = null;
                try {
                    XSSFSheet sheet = workbook.getSheetAt(0);
                    sheet.iterator();
                    if (workbook != null) {
                        if (0 != 0) {
                            try {
                                workbook.close();
                            } catch (Throwable th3) {
                                th2.addSuppressed(th3);
                            }
                        } else {
                            workbook.close();
                        }
                    }
                    if (fis != null) {
                        if (0 != 0) {
                            try {
                                fis.close();
                            } catch (Throwable th4) {
                                th.addSuppressed(th4);
                            }
                        } else {
                            fis.close();
                        }
                    }
                } catch (Throwable th5) {
                    if (workbook != null) {
                        if (0 != 0) {
                            try {
                                workbook.close();
                            } catch (Throwable th6) {
                                th2.addSuppressed(th6);
                            }
                        } else {
                            workbook.close();
                        }
                    }
                    throw th5;
                }
            } finally {
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
