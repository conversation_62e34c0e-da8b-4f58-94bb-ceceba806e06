package cn.rasp.vuln.controller;

import cn.rasp.vuln.async.AsyncComponent;
import java.util.Map;
import java.util.concurrent.Future;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/async/"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/AsyncController.class */
public class AsyncController {

    @Autowired
    private AsyncComponent asyncComponent;

    @GetMapping({"/get/cmd"})
    public String getCmd(String cmd, HttpServletResponse response) throws Exception {
        Future<String> future = this.asyncComponent.asyncCmd(cmd);
        while (!future.isDone()) {
            Thread.sleep(1000L);
        }
        String result = future.get();
        if (result != null && result.equals("fail")) {
            response.setStatus(403);
            return result;
        }
        return result;
    }

    @GetMapping({"/get/sql"})
    public Map<String, Object> getSql(String username, HttpServletResponse response) throws Exception {
        Future<Map<String, Object>> future = this.asyncComponent.asyncSql(username);
        while (!future.isDone()) {
            Thread.sleep(500L);
        }
        Map<String, Object> result = future.get();
        if (result == null) {
            response.setStatus(403);
            return null;
        }
        return result;
    }
}
