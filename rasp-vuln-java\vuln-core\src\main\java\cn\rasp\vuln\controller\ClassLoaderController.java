package cn.rasp.vuln.controller;

import cn.rasp.vuln.classloader.ClassLoader;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/classloader/"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/ClassLoaderController.class */
public class ClassLoaderController {
    @PostMapping({"/defineClass"})
    public ResponseEntity<?> defineClass(String bytes) {
        return ResponseEntity.ok(ClassLoader.defineClass(bytes));
    }

    @PostMapping({"/bcel"})
    public ResponseEntity<?> bcel(String bytes) {
        return ResponseEntity.ok(ClassLoader.bcel(bytes));
    }
}
