package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractCommonController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.ssrf.URLConnect;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: SSRFController.java */
@RequestMapping({"/ssrf/common_http_client"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/CommonHttpClientController.class */
class CommonHttpClientController extends AbstractCommonController {
    CommonHttpClientController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(URLConnect.commonHttpClient(payload.getPayload()));
    }
}
