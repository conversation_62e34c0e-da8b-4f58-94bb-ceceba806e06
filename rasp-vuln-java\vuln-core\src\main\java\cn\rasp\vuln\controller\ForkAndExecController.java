package cn.rasp.vuln.controller;

import cn.rasp.vuln.cmd.CommandExecution;
import cn.rasp.vuln.controller.common.AbstractFullController;
import cn.rasp.vuln.entity.AttackPayload;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: CmdController.java */
@RequestMapping({"/cmd/fork_exec"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/ForkAndExecController.class */
class ForkAndExecController extends AbstractFullController {
    ForkAndExecController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(CommandExecution.forkAndExec(payload.getPayload()));
    }
}
