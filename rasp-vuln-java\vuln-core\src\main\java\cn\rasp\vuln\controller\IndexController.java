package cn.rasp.vuln.controller;

import java.io.IOException;
import java.util.Map;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.MatrixVariable;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping({"index"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/IndexController.class */
public class IndexController {
    @RequestMapping({"/hello"})
    public ResponseEntity<?> hello() {
        return ResponseEntity.ok("hello");
    }

    @RequestMapping({"/gc"})
    public ResponseEntity<?> gc() {
        System.gc();
        return ResponseEntity.ok("gc");
    }

    @GetMapping({"/matrixMap/{id}"})
    public ResponseEntity<?> matrixMap(@MatrixVariable(pathVar = "id") Map<String, String> map) {
        return ResponseEntity.ok(map);
    }

    @GetMapping({"/matrixName/{id}"})
    public ResponseEntity<?> matrixName(@MatrixVariable(pathVar = "id") String payload) {
        return ResponseEntity.ok(payload);
    }

    @GetMapping({"/pathMap/{payload}"})
    public ResponseEntity<?> pathMap(@PathVariable Map<String, String> payload) {
        return ResponseEntity.ok(payload);
    }

    @GetMapping({"/pathName/{payload}"})
    public ResponseEntity<?> pathName(@PathVariable(name = "payload") String payload) {
        return ResponseEntity.ok(payload);
    }

    @PostMapping({"/multipart"})
    public ResponseEntity<?> multipart(MultipartFile file, Map<String, Object> params, @RequestParam("payload") String[] payloads) {
        params.put("fileName", file.getOriginalFilename());
        params.put("payload", payloads);
        return ResponseEntity.ok(params);
    }

    @PostMapping({"/read1"})
    public void read1(HttpServletRequest request) throws Exception {
        ServletInputStream inputStream = request.getInputStream();
        byte[] buffer = new byte[1024];
        int totalBytesRead = 0;
        while (true) {
            try {
                int bytesRead = inputStream.read(buffer, totalBytesRead, 1024 - totalBytesRead);
                if (bytesRead == -1) {
                    break;
                }
                totalBytesRead += bytesRead;
                if (totalBytesRead == 1024) {
                    processRequestBody(buffer, totalBytesRead);
                    totalBytesRead = 0;
                }
            } catch (IOException e) {
                e.printStackTrace();
                return;
            }
        }
        if (totalBytesRead > 0) {
            processRequestBody(buffer, totalBytesRead);
        }
    }

    private void processRequestBody(byte[] data, int length) {
        String requestBody = new String(data, 0, length);
        System.out.println("Request body: " + requestBody);
    }
}
