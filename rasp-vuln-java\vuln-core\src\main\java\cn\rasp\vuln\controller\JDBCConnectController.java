package cn.rasp.vuln.controller;

import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.jdbc.Connection;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/jdbc"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/JDBCConnectController.class */
public class JDBCConnectController {
    @PostMapping({"/h2"})
    public ResponseEntity<?> h2(AttackPayload attackPayload) throws Exception {
        Connection.h2(attackPayload.getPayload());
        return ResponseEntity.ok("success");
    }

    @PostMapping({"/mysql"})
    public ResponseEntity<?> mysql(AttackPayload attackPayload) throws Exception {
        Connection.mysql(attackPayload.getPayload());
        return ResponseEntity.ok("success");
    }

    @PostMapping({"/db2"})
    public ResponseEntity<?> db2(AttackPayload attackPayload) throws Exception {
        Connection.db2(attackPayload.getPayload());
        return ResponseEntity.ok("success");
    }

    @PostMapping({"/derby"})
    public ResponseEntity<?> derby(AttackPayload attackPayload) throws Exception {
        Connection.derby(attackPayload.getPayload());
        return ResponseEntity.ok("success");
    }

    @PostMapping({"/modeshape"})
    public ResponseEntity<?> modeshape(AttackPayload attackPayload) throws Exception {
        Connection.modeshape(attackPayload.getPayload());
        return ResponseEntity.ok("success");
    }

    @PostMapping({"/postgres"})
    public ResponseEntity<?> postgres(AttackPayload attackPayload) throws Exception {
        Connection.postgres(attackPayload.getPayload());
        return ResponseEntity.ok("success");
    }

    @PostMapping({"/oracle"})
    public ResponseEntity<?> oracle(AttackPayload attackPayload) throws Exception {
        Connection.oracle(attackPayload.getPayload());
        return ResponseEntity.ok("success");
    }

    @PostMapping({"/mssql"})
    public ResponseEntity<?> mssql(AttackPayload attackPayload) throws Exception {
        Connection.mssql(attackPayload.getPayload());
        return ResponseEntity.ok("success");
    }

    @PostMapping({"/mariadb"})
    public ResponseEntity<?> mariadb(AttackPayload attackPayload) throws Exception {
        Connection.mariadb(attackPayload.getPayload());
        return ResponseEntity.ok("success");
    }
}
