package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractCommonController;
import cn.rasp.vuln.entity.AttackPayload;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/jdbc/"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/JDBCController.class */
public class JDBCController extends AbstractCommonController {

    @Autowired
    protected JdbcTemplate jdbcTemplate;

    @Autowired
    @Qualifier("mysqlJdbcTemplate")
    protected JdbcTemplate mysqlJdbcTemplate;

    @Autowired
    @Qualifier("mssqlJdbcTemplate")
    protected JdbcTemplate mssqlJdbcTemplate;

    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;

    @Autowired
    @Qualifier("postgresqlJdbcTemplate")
    protected JdbcTemplate postgresqlJdbcTemplate;

    @Autowired
    @Qualifier("kingbasesqlJdbcTemplate")
    protected JdbcTemplate kingbasesqlJdbcTemplate;

    @Autowired
    @Qualifier("dmJdbcTemplate")
    protected JdbcTemplate dmJdbcTemplate;

    @Autowired
    @Qualifier("hsqldbJdbcTemplate")
    protected JdbcTemplate hsqldbJdbcTemplate;

    @Autowired
    @Qualifier("gbaseJdbcTemplate")
    protected JdbcTemplate gbaseJdbcTemplate;

    @Autowired
    @Qualifier("oceanBaseJdbcTemplate")
    protected JdbcTemplate oceanBaseJdbcTemplate;

    @Autowired
    @Qualifier("gaussDBJdbcTemplate")
    protected JdbcTemplate gaussDBJdbcTemplate;

    @Autowired
    @Qualifier("tiDBJdbcTemplate")
    protected JdbcTemplate tiDBJdbcTemplate;

    @PostMapping({"/oracle/search"})
    public ResponseEntity<?> oracleSearch(String sql) {
        return ResponseEntity.ok(this.oracleJdbcTemplate.queryForList(sql));
    }

    @PostMapping({"/mysql/search"})
    public ResponseEntity<?> mysqlSearch(String sql) {
        return ResponseEntity.ok(this.mysqlJdbcTemplate.queryForList(sql));
    }

    @GetMapping({"/oracle/students"})
    public ResponseEntity<?> oracleStudent(String name) {
        String sql = "select * from student where name ='" + name + "'";
        return ResponseEntity.ok(this.oracleJdbcTemplate.queryForList(sql));
    }

    private Map<String, Object> getSysUserByUsername(String username, String type) {
        String sql = "select * from sys_user where username = '" + username + "'";
        JdbcTemplate template = this.jdbcTemplate;
        if ("mysql".equals(type)) {
            template = this.mysqlJdbcTemplate;
        }
        if ("mssql".equals(type)) {
            template = this.mssqlJdbcTemplate;
        } else if ("oracle".equals(type)) {
            template = this.oracleJdbcTemplate;
        } else if ("postgresql".equals(type)) {
            template = this.postgresqlJdbcTemplate;
        } else if ("kingbase".equals(type)) {
            template = this.kingbasesqlJdbcTemplate;
            sql = "select * from \"public\".sys_user where username = '" + username + "'";
        } else if ("dm".equals(type)) {
            template = this.dmJdbcTemplate;
            sql = "SELECT * FROM \"sys_user\" t WHERE username = '" + username + "'";
        } else if ("hsqldb".equals(type)) {
            template = this.hsqldbJdbcTemplate;
            sql = "SELECT * FROM \"sys_user\" t WHERE username = '" + username + "'";
        } else if ("gbase".equals(type)) {
            template = this.gbaseJdbcTemplate;
            sql = "SELECT * FROM \"sys_user\" t WHERE username = '" + username + "'";
        } else if ("oceanbase".equals(type)) {
            template = this.oceanBaseJdbcTemplate;
        } else if ("gaussdb".equals(type)) {
            template = this.gaussDBJdbcTemplate;
        } else if ("tidb".equals(type)) {
            template = this.tiDBJdbcTemplate;
        }
        return template.queryForMap(sql);
    }

    @PostMapping({"/jsonBypass"})
    public Map<String, Object> jsonBypass(HttpServletRequest request, String json, String type) {
        int size;
        String contentType = request.getContentType();
        Map<String, Object> data = new LinkedHashMap<>();
        Map<String, String> map = null;
        if (contentType != null && contentType.contains("application/json") && json != null && (size = request.getContentLength()) > 0) {
            byte[] buf = new byte[size];
            try {
                ServletInputStream inputStream = request.getInputStream();
                inputStream.read(buf);
                inputStream.close();
                String postdata = new String(buf);
                if ("fastjson".equals(json)) {
                    map = (Map) JSONObject.parseObject(postdata, new TypeReference<Map<String, String>>() { // from class: cn.rasp.vuln.controller.JDBCController.1
                        AnonymousClass1() {
                        }
                    }.getType(), new Feature[0]);
                }
                if ("jackson".equals(json)) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    com.fasterxml.jackson.core.type.TypeReference<Map<String, String>> typeRef = new com.fasterxml.jackson.core.type.TypeReference<Map<String, String>>() { // from class: cn.rasp.vuln.controller.JDBCController.2
                        AnonymousClass2() {
                        }
                    };
                    map = (Map) objectMapper.readValue(postdata, typeRef);
                }
                if ("gson".equals(json)) {
                    Gson gson = new Gson();
                    map = (Map) gson.fromJson(postdata, new TypeToken<Map<String, String>>() { // from class: cn.rasp.vuln.controller.JDBCController.3
                        AnonymousClass3() {
                        }
                    }.getType());
                }
                if (map != null) {
                    String username = map.get("username");
                    return getSysUserByUsername(username, type);
                }
            } catch (Exception e) {
                data.put("msg", e.getMessage());
            }
        }
        return data;
    }

    /* renamed from: cn.rasp.vuln.controller.JDBCController$1 */
    /* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/JDBCController$1.class */
    class AnonymousClass1 extends TypeReference<Map<String, String>> {
        AnonymousClass1() {
        }
    }

    /* renamed from: cn.rasp.vuln.controller.JDBCController$2 */
    /* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/JDBCController$2.class */
    class AnonymousClass2 extends com.fasterxml.jackson.core.type.TypeReference<Map<String, String>> {
        AnonymousClass2() {
        }
    }

    /* renamed from: cn.rasp.vuln.controller.JDBCController$3 */
    /* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/JDBCController$3.class */
    class AnonymousClass3 extends TypeToken<Map<String, String>> {
        AnonymousClass3() {
        }
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(getSysUserByUsername(payload.getArg1(), payload.getArg2()));
    }

    @GetMapping({"forloop"})
    public ResponseEntity<?> forloop(AttackPayload payload) {
        ArrayList<Map<String, Object>> maps = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            maps.add(getSysUserByUsername(payload.getArg1(), payload.getArg2()));
        }
        return ResponseEntity.ok(maps);
    }
}
