package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractPostBodyController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.jndi.Log4J;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: JNDIController.java */
@RequestMapping({"/log4j"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/Log4JController.class */
class Log4JController extends AbstractPostBodyController {
    Log4JController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        Log4J.info(payload.getPayload());
        return ResponseEntity.ok("");
    }
}
