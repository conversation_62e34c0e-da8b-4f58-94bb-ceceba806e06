package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractCommonController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.ssrf.URLConnect;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: SSRFController.java */
@RequestMapping({"/ssrf/netty"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/NettyController.class */
class NettyController extends AbstractCommonController {
    NettyController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        URLConnect.netty(payload.getArg1(), payload.getArg2());
        return ResponseEntity.ok("连接成功");
    }
}
