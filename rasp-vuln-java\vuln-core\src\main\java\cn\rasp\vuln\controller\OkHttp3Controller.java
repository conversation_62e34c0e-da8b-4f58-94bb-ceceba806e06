package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractCommonController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.ssrf.URLConnect;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: SSRFController.java */
@RequestMapping({"/ssrf/ok_http3"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/OkHttp3Controller.class */
class OkHttp3Controller extends AbstractCommonController {
    OkHttp3Controller() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(URLConnect.okHttp3(payload.getPayload()));
    }
}
