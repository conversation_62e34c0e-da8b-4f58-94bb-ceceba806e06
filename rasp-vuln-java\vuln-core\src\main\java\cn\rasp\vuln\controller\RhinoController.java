package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractPostBodyController;
import cn.rasp.vuln.engine.ScriptEngine;
import cn.rasp.vuln.entity.AttackPayload;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: ScriptEngineController.java */
@RequestMapping({"/script_engine/rhino/"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/RhinoController.class */
class RhinoController extends AbstractPostBodyController {
    RhinoController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(ScriptEngine.rhinoEval(payload.getPayload()));
    }
}
