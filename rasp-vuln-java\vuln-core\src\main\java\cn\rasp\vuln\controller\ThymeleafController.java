package cn.rasp.vuln.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@RequestMapping({"/ssti/thymeleaf"})
@Controller
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/ThymeleafController.class */
public class ThymeleafController {
    @GetMapping({"/"})
    public String index(Model model) {
        model.addAttribute("message", "happy birthday");
        return "welcome";
    }

    @GetMapping({"/path"})
    public String path(@RequestParam String lang) {
        return "user/" + lang + "/welcome";
    }

    @GetMapping({"/fragment"})
    public String fragment(@RequestParam String section) {
        return "welcome :: " + section;
    }

    @GetMapping({"/user/{lang}"})
    public String getHome(@PathVariable String lang) {
        return "user/" + lang + "/welcome";
    }
}
