package cn.rasp.vuln.controller;

import cn.rasp.vuln.controller.common.AbstractPostBodyController;
import cn.rasp.vuln.deserialization.Deserialization;
import cn.rasp.vuln.entity.AttackPayload;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: DeserializationController.java */
@RequestMapping({"/deserialization/xml/x_stream"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/XStreamController.class */
class XStreamController extends AbstractPostBodyController {
    XStreamController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        Object o = Deserialization.xStream(payload.getPayload());
        return ResponseEntity.ok(o);
    }
}
