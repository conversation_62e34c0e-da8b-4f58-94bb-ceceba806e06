package cn.rasp.vuln.controller;

import cn.rasp.vuln.entity.UploadDAO;
import cn.rasp.vuln.entity.UploadData;
import cn.rasp.vuln.utils.UploadDataListener;
import cn.rasp.vuln.xxe.XMLParser;
import com.alibaba.excel.EasyExcel;
import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping({"/xxe/"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/XXEController.class */
public class XXEController {

    @Autowired
    private UploadDAO uploadDAO;

    @RequestMapping({"/excel"})
    @ResponseBody
    public String nioUpload(@RequestParam("file") MultipartFile uploadFile) throws IOException {
        EasyExcel.read(uploadFile.getInputStream(), UploadData.class, new UploadDataListener(this.uploadDAO)).sheet().doRead();
        return "success";
    }

    @RequestMapping({"/excel/poi"})
    @ResponseBody
    public String poiExcel() throws Exception {
        XMLParser.excel("path/to/excel");
        return "success";
    }
}
