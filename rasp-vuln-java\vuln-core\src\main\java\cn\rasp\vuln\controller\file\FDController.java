package cn.rasp.vuln.controller.file;

import java.io.File;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.OpenOption;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.javaweb.utils.FileUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/file/FDController.class */
public class FDController {
    @GetMapping({"file/download1"})
    public void download(String path, HttpServletResponse response) throws Exception {
        File file = new File(path);
        response.addHeader("Content-Type", "application/octet-stream");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getName(), "utf-8"));
        byte[] bytes = FileUtils.readFileToByteArray(file);
        ServletOutputStream outputStream = response.getOutputStream();
        outputStream.write(bytes);
        outputStream.close();
    }

    @GetMapping({"file/download2"})
    public void download2(String path, HttpServletResponse response) throws Exception {
        File file = new File(path);
        response.addHeader("Content-Type", "application/octet-stream");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getName(), "utf-8"));
        InputStream fileInputStream = Files.newInputStream(file.toPath(), new OpenOption[0]);
        ServletOutputStream outputStream = response.getOutputStream();
        IOUtils.copy(fileInputStream, outputStream);
        fileInputStream.close();
        outputStream.close();
    }
}
