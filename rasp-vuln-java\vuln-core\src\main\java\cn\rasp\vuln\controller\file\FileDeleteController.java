package cn.rasp.vuln.controller.file;

import cn.rasp.vuln.controller.common.AbstractCommonController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.file.FileAccess;
import java.io.IOException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: FileController.java */
@RequestMapping({"/file/delete"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/file/FileDeleteController.class */
class FileDeleteController extends AbstractCommonController {
    FileDeleteController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws IOException {
        return ResponseEntity.ok(Boolean.valueOf(FileAccess.IOFile.delete(payload.getPayload())));
    }
}
