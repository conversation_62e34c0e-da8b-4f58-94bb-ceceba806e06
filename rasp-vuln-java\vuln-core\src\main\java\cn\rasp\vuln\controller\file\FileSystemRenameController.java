package cn.rasp.vuln.controller.file;

import cn.rasp.vuln.controller.common.AbstractCommonController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.file.FileAccess;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: FileSystemController.java */
@RequestMapping({"/file/file_system/rename"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/file/FileSystemRenameController.class */
class FileSystemRenameController extends AbstractCommonController {
    FileSystemRenameController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        return ResponseEntity.ok(Boolean.valueOf(FileAccess.FileSystem.rename(payload.getArg1(), payload.getArg2())));
    }
}
