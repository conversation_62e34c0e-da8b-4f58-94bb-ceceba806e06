package cn.rasp.vuln.controller.file;

import java.io.File;
import java.io.InputStream;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.io.FileUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping({"/file/upload"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/file/FileUploadController.class */
public class FileUploadController {
    @PostMapping
    public ResponseEntity<?> upload(MultipartFile file, HttpServletRequest request) throws Exception {
        InputStream inputStream = file.getInputStream();
        String uploadFolder = request.getSession().getServletContext().getRealPath("/") + "/uploads/";
        String filename = file.getOriginalFilename();
        File uploadFile = new File(uploadFolder, filename);
        FileUtils.copyInputStreamToFile(inputStream, uploadFile);
        return ResponseEntity.ok(uploadFile.getAbsolutePath() + " 上传成功");
    }
}
