package cn.rasp.vuln.controller.file;

import cn.rasp.vuln.controller.common.AbstractCommonController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.file.FileAccess;
import java.io.File;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: NIOFileController.java */
@RequestMapping({"/file/nio/create"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/file/NIOFileCreateFile.class */
class NIOFileCreateFile extends AbstractCommonController {
    NIOFileCreateFile() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        FileAccess.NIOFile.createFile(payload.getPayload());
        return ResponseEntity.ok(new File(payload.getPayload()).getAbsolutePath());
    }
}
