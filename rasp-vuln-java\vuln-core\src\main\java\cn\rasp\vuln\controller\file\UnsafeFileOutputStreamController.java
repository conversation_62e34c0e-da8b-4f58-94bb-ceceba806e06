package cn.rasp.vuln.controller.file;

import cn.rasp.vuln.controller.common.AbstractCommonController;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.file.FileAccess;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: FileController.java */
@RequestMapping({"/file/unsafe_file_output_stream"})
@RestController
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/controller/file/UnsafeFileOutputStreamController.class */
class UnsafeFileOutputStreamController extends AbstractCommonController {
    UnsafeFileOutputStreamController() {
    }

    @Override // cn.rasp.vuln.controller.common.CommonController
    public ResponseEntity<?> execute(AttackPayload payload) throws Exception {
        FileAccess.IOFile.unsafeWrite(payload.getArg1(), payload.getArg2());
        return ResponseEntity.ok(payload.getArg1());
    }
}
