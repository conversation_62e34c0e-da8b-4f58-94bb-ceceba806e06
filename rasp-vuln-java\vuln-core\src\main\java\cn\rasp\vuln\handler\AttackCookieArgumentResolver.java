package cn.rasp.vuln.handler;

import cn.rasp.vuln.annotation.AttackCookie;
import cn.rasp.vuln.entity.AttackPayload;
import cn.rasp.vuln.utils.ReflectionUtils;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.util.UrlPathHelper;
import org.springframework.web.util.WebUtils;

/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/handler/AttackCookieArgumentResolver.class */
public class AttackCookieArgumentResolver implements HandlerMethodArgumentResolver {
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.hasParameterAnnotation(AttackCookie.class);
    }

    private String getArg(String argName, NativeWebRequest nativeWebRequest) throws Exception {
        Object servletRequest = getServletRequest(nativeWebRequest);
        Object cookie = ReflectionUtils.invokeMethod(null, WebUtils.class, "getCookie", new Class[]{getServletRequestClass(), String.class}, servletRequest, argName);
        if (cookie != null) {
            String cookieValue = (String) cookie.getClass().getMethod("getValue", new Class[0]).invoke(cookie, new Object[0]);
            return (String) ReflectionUtils.invokeMethod(new UrlPathHelper(), "decodeRequestString", new Class[]{getServletRequestClass(), String.class}, servletRequest, cookieValue);
        }
        return null;
    }

    private Class<?> getServletRequestClass() throws ClassNotFoundException {
        try {
            return Class.forName("jakarta.servlet.http.HttpServletRequest");
        } catch (Exception e) {
            return Class.forName("javax.servlet.http.HttpServletRequest");
        }
    }

    private Object getServletRequest(NativeWebRequest nativeWebRequest) throws Exception {
        return ReflectionUtils.invokeMethod(nativeWebRequest, "getNativeRequest", new Class[]{Class.class}, getServletRequestClass());
    }

    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        AttackPayload result = new AttackPayload();
        result.setPayload(getArg("payload", nativeWebRequest));
        result.setEncrypt(Boolean.valueOf(getArg("encrypt", nativeWebRequest)));
        result.setArg1(getArg("arg1", nativeWebRequest));
        result.setArg2(getArg("arg2", nativeWebRequest));
        result.setArg3(getArg("arg3", nativeWebRequest));
        return result;
    }
}
