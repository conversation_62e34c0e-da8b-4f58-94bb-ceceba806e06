package cn.rasp.vuln.handler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/handler/GlobalExceptionHandler.class */
public class GlobalExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler({Exception.class})
    public ResponseEntity<?> exception(Exception exception) {
        log.error("global exception", exception);
        String msg = getExceptionMsg(exception);
        if (msg.contains("RASP")) {
            return ResponseEntity.status(403).body(msg);
        }
        return ResponseEntity.status(500).body(msg);
    }

    private String getExceptionMsg(Throwable e) {
        if (e == null) {
            return "";
        }
        String err = e.getClass() + ": " + e.getMessage() + "\n";
        return err + getExceptionMsg(e.getCause());
    }
}
