package cn.rasp.vuln.service.impl;

import cn.rasp.vuln.service.IUserService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

@Service
/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/service/impl/UserServiceImpl.class */
public class UserServiceImpl implements IUserService {

    @Autowired
    protected JdbcTemplate jdbcTemplate;

    @Override // cn.rasp.vuln.service.IUserService
    public Map<String, Object> select(String username) throws Exception {
        String sql = "select * from sys_user where username = '" + username + "'";
        return this.jdbcTemplate.queryForMap(sql);
    }
}
