package cn.rasp.vuln.utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.net.ssl.SSLSocketFactory;
import org.apache.commons.io.IOUtils;
import org.javaweb.net.ChunkedInputStream;

/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/utils/HttpClient.class */
public class HttpClient {
    private static final int MAX_HEADER_LENGTH = 4096000;

    private static String[] parseHost(String host, boolean isSSL) {
        String port = isSSL ? "443" : "80";
        String[] hostArray = host.split(":");
        if (hostArray.length == 2) {
            host = hostArray[0];
            port = hostArray[1];
        }
        return new String[]{host, port};
    }

    private static Map<String, String> parseHeader(DataInputStream dis) throws IOException {
        Map<String, String> header = new LinkedHashMap<>();
        while (true) {
            String str = dis.readLine();
            if (str == null || "".equals(str)) {
                break;
            }
            String[] strArray = str.split(":\\s+");
            if (strArray.length == 2) {
                header.put(strArray[0], strArray[1]);
            }
        }
        return header;
    }

    public static byte[] parseBody(DataInputStream dis) throws IOException {
        int previous = 0;
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        while (true) {
            int a = dis.read();
            if (a != -1) {
                if (a == 10 && previous != 13) {
                    baos.write(13);
                }
                baos.write(a);
                previous = a;
            } else {
                return baos.toByteArray();
            }
        }
    }

    public static void proxyRequest(String req, boolean isSSL, Map<String, Object> data) throws IOException {
        Socket socket;
        byte[] bytes = req.getBytes();
        DataInputStream dis = new DataInputStream(new ByteArrayInputStream(bytes));
        String line = dis.readLine();
        if (line != null) {
            Map<String, String> header = parseHeader(dis);
            byte[] body = parseBody(dis);
            String[] hosts = parseHost(header.get("Host"), isSSL);
            String contentLen = header.get("Content-Length");
            if (contentLen != null && Integer.parseInt(contentLen) > 0) {
                header.put("Content-Length", String.valueOf(body.length));
            }
            if (isSSL) {
                SSLSocketFactory factory = (SSLSocketFactory) SSLSocketFactory.getDefault();
                socket = factory.createSocket(hosts[0], Integer.parseInt(hosts[1]));
            } else {
                socket = new Socket(hosts[0], Integer.parseInt(hosts[1]));
            }
            OutputStream out = socket.getOutputStream();
            out.write((line + "\r\n").getBytes());
            for (String key : header.keySet()) {
                out.write((key + ": " + header.get(key) + "\r\n").getBytes());
            }
            out.write("\r\n".getBytes());
            if (body.length > 0) {
                out.write(body);
            }
            out.flush();
            InputStream responseIn = socket.getInputStream();
            DataInputStream responseDis = new DataInputStream(responseIn);
            String status = responseDis.readLine();
            Map<String, String> responseHeader = parseHeader(responseDis);
            InputStream respIn = parseResponseInputStream(responseIn, responseHeader);
            data.put("status", status);
            data.put("header", responseHeader);
            data.put("body", IOUtils.toString(respIn));
            socket.close();
        }
    }

    private static InputStream parseResponseInputStream(InputStream responseIn, Map<String, String> responseHeader) throws IOException {
        String transferEncoding = responseHeader.get("Transfer-Encoding");
        String responseContentLength = responseHeader.get("Content-Length");
        InputStream in = new HttpInputStream(responseContentLength, responseIn);
        if ("chunked".equalsIgnoreCase(transferEncoding)) {
            return new ChunkedInputStream(new DataInputStream(in));
        }
        return in;
    }

    /* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/utils/HttpClient$HttpInputStream.class */
    static class HttpInputStream extends InputStream {
        private final InputStream in;
        private long maxLength;
        private long readLength = 0;

        public HttpInputStream(String contentLength, InputStream in) {
            this.maxLength = -1L;
            if (contentLength != null) {
                this.maxLength = Long.parseLong(contentLength);
            }
            this.in = in;
        }

        @Override // java.io.InputStream
        public int read() throws IOException {
            if (this.maxLength == this.readLength) {
                return -1;
            }
            this.readLength++;
            return this.in.read();
        }
    }
}
