package cn.rasp.vuln.utils;

import java.lang.reflect.Method;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/utils/HttpServletRequestUtils.class */
public class HttpServletRequestUtils {
    private static Method getRequestMethod = null;
    private static Method getResponseMethod = null;
    private static Method getSessionMethod = null;
    private static Method setAttributeMethod = null;
    private static Method getRemoteAddr = null;
    private static Method getRequestURL = null;
    private static Method getServerName = null;
    private static Method getServerPort = null;
    private static Method getContextPath = null;
    private static Method getScheme = null;

    public static Object getHttpServletRequest() {
        ServletRequestAttributes attributes = RequestContextHolder.currentRequestAttributes();
        Class<?> attributesClass = attributes.getClass();
        if (getRequestMethod == null) {
            getRequestMethod = org.springframework.util.ReflectionUtils.findMethod(attributesClass, "getRequest");
        }
        return org.springframework.util.ReflectionUtils.invokeMethod(getRequestMethod, attributes);
    }

    public static Object getHttpServletResponse() {
        ServletRequestAttributes attributes = RequestContextHolder.currentRequestAttributes();
        Class<?> attributesClass = attributes.getClass();
        if (getResponseMethod == null) {
            getResponseMethod = org.springframework.util.ReflectionUtils.findMethod(attributesClass, "getResponse");
        }
        return org.springframework.util.ReflectionUtils.invokeMethod(getResponseMethod, attributes);
    }

    public static Object getHttpSession() {
        Object request = getHttpServletRequest();
        Class<?> requestClass = request.getClass();
        if (getSessionMethod == null) {
            getSessionMethod = org.springframework.util.ReflectionUtils.findMethod(requestClass, "getSession");
        }
        return org.springframework.util.ReflectionUtils.invokeMethod(getSessionMethod, request);
    }

    public static void setSessionAttribute(String name, Object value) {
        Object session = getHttpSession();
        if (session == null) {
            throw new NullPointerException("无法获取Session对象");
        }
        Class<?> sessionClass = session.getClass();
        if (setAttributeMethod == null) {
            setAttributeMethod = org.springframework.util.ReflectionUtils.findMethod(sessionClass, "setAttribute", new Class[]{String.class, Object.class});
        }
        org.springframework.util.ReflectionUtils.invokeMethod(setAttributeMethod, session, new Object[]{name, value});
    }

    public static String getRemoteAddr() {
        Object request = getHttpServletRequest();
        Class<?> requestClass = request.getClass();
        if (getRemoteAddr == null) {
            getRemoteAddr = org.springframework.util.ReflectionUtils.findMethod(requestClass, "getRemoteAddr");
        }
        return (String) org.springframework.util.ReflectionUtils.invokeMethod(getRemoteAddr, request);
    }

    public static StringBuffer getRequestURL() {
        Object request = getHttpServletRequest();
        Class<?> requestClass = request.getClass();
        if (getRequestURL == null) {
            getRequestURL = org.springframework.util.ReflectionUtils.findMethod(requestClass, "getRequestURL");
        }
        return (StringBuffer) org.springframework.util.ReflectionUtils.invokeMethod(getRequestURL, request);
    }

    public static String getServerName() {
        Object request = getHttpServletRequest();
        Class<?> requestClass = request.getClass();
        if (getServerName == null) {
            getServerName = org.springframework.util.ReflectionUtils.findMethod(requestClass, "getServerName");
        }
        return (String) org.springframework.util.ReflectionUtils.invokeMethod(getServerName, request);
    }

    public static int getServerPort() {
        Object request = getHttpServletRequest();
        Class<?> requestClass = request.getClass();
        if (getServerPort == null) {
            getServerPort = org.springframework.util.ReflectionUtils.findMethod(requestClass, "getServerPort");
        }
        return ((Integer) org.springframework.util.ReflectionUtils.invokeMethod(getServerPort, request)).intValue();
    }

    public static String getContextPath() {
        Object request = getHttpServletRequest();
        Class<?> requestClass = request.getClass();
        if (getContextPath == null) {
            getContextPath = org.springframework.util.ReflectionUtils.findMethod(requestClass, "getContextPath");
        }
        return (String) org.springframework.util.ReflectionUtils.invokeMethod(getContextPath, request);
    }

    public static String getScheme() {
        Object request = getHttpServletRequest();
        Class<?> requestClass = request.getClass();
        if (getScheme == null) {
            getScheme = org.springframework.util.ReflectionUtils.findMethod(requestClass, "getScheme");
        }
        return (String) org.springframework.util.ReflectionUtils.invokeMethod(getScheme, request);
    }

    public static String getAppURL() {
        String scheme = getScheme();
        String contextPath = getContextPath();
        int serverPort = getServerPort();
        String serverName = getServerName();
        return scheme + "://" + serverName + ":" + serverPort + contextPath;
    }
}
