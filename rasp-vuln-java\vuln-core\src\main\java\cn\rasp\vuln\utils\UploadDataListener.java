package cn.rasp.vuln.utils;

import cn.rasp.vuln.entity.UploadDAO;
import cn.rasp.vuln.entity.UploadData;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import java.util.ArrayList;
import java.util.List;

/* loaded from: vuln-core-3.0.3.jar:cn/rasp/vuln/utils/UploadDataListener.class */
public class UploadDataListener extends AnalysisEventListener<UploadData> {
    private static final int BATCH_COUNT = 5;
    List<UploadData> list;
    private UploadDAO uploadDAO;

    public UploadDataListener() {
        this.list = new ArrayList();
        this.uploadDAO = new UploadDAO();
    }

    public UploadDataListener(UploadDAO uploadDAO) {
        this.list = new ArrayList();
        this.uploadDAO = uploadDAO;
    }

    public void invoke(UploadData data, AnalysisContext context) {
        this.list.add(data);
        if (this.list.size() >= BATCH_COUNT) {
            saveData();
            this.list.clear();
        }
    }

    public void doAfterAllAnalysed(AnalysisContext context) {
        saveData();
    }

    private void saveData() {
        this.uploadDAO.save(this.list);
    }
}
