package cn.rasp.vuln;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * RASP Vulnerability Testing Platform
 * Spring Boot Main Application Class
 */
@SpringBootApplication
@ComponentScan(basePackages = {"cn.rasp.vuln"})
@EntityScan(basePackages = {"cn.rasp.vuln.entity"})
@EnableJpaRepositories(basePackages = {"cn.rasp.vuln.repository"})
public class RaspVulnApplication {

    public static void main(String[] args) {
        System.setProperty("java.awt.headless", "true");
        SpringApplication.run(RaspVulnApplication.class, args);
    }
}
