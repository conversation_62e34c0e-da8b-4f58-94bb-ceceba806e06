server:
  port: 8081
  servlet:
    context-path: /
  tomcat:
    max-http-form-post-size: 100MB
    max-swallow-size: 100MB

spring:
  application:
    name: rasp-vuln-java
  
  # Database Configuration
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # H2 Console Configuration
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
    open-in-view: false
  
  # Web Configuration
  web:
    resources:
      static-locations: classpath:/static/
  
  # Servlet Configuration
  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 2KB
  
  # Thymeleaf Configuration
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html
  
  # Jackson Configuration
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# Logging Configuration
logging:
  level:
    root: INFO
    cn.rasp.vuln: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Management Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# Custom Configuration
rasp:
  vuln:
    # Enable/disable specific vulnerability modules
    modules:
      cmd: true
      deserialization: true
      expression: true
      file: true
      jdbc: true
      jndi: true
      memshell: true
      ssrf: true
      ssti: true
      xpath: true
      xxe: true
    # Security settings
    security:
      enable-csrf: false
      enable-cors: true
