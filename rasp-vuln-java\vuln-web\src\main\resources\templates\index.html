<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title}">RASP Vulnerability Testing Platform</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .version {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
        }
        .modules {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .module {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 20px;
            background-color: #fafafa;
        }
        .module h3 {
            margin-top: 0;
            color: #444;
        }
        .module ul {
            margin: 0;
            padding-left: 20px;
        }
        .module li {
            margin: 5px 0;
        }
        .module a {
            color: #007bff;
            text-decoration: none;
        }
        .module a:hover {
            text-decoration: underline;
        }
        .info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 th:text="${title}">RASP Vulnerability Testing Platform</h1>
        <div class="version">Version: <span th:text="${version}">3.0.3</span></div>
        
        <div class="info">
            <strong>Welcome to RASP Vulnerability Testing Platform!</strong><br>
            This platform provides various vulnerability testing endpoints for RASP (Runtime Application Self-Protection) testing.
        </div>

        <div class="modules">
            <div class="module">
                <h3>Command Execution</h3>
                <ul>
                    <li><a href="/cmd/exec">Command Execution</a></li>
                    <li><a href="/cmd/processbuilder">ProcessBuilder</a></li>
                    <li><a href="/cmd/runtime">Runtime Exec</a></li>
                </ul>
            </div>

            <div class="module">
                <h3>Deserialization</h3>
                <ul>
                    <li><a href="/deserialization/fastjson">FastJSON</a></li>
                    <li><a href="/deserialization/jackson">Jackson</a></li>
                    <li><a href="/deserialization/hessian">Hessian</a></li>
                    <li><a href="/deserialization/xstream">XStream</a></li>
                </ul>
            </div>

            <div class="module">
                <h3>Expression Language</h3>
                <ul>
                    <li><a href="/expression/ognl">OGNL</a></li>
                    <li><a href="/expression/spEl">SpEL</a></li>
                    <li><a href="/expression/mvel">MVEL</a></li>
                    <li><a href="/expression/groovy">Groovy</a></li>
                </ul>
            </div>

            <div class="module">
                <h3>File Operations</h3>
                <ul>
                    <li><a href="/file/read">File Read</a></li>
                    <li><a href="/file/write">File Write</a></li>
                    <li><a href="/file/upload">File Upload</a></li>
                </ul>
            </div>

            <div class="module">
                <h3>JDBC</h3>
                <ul>
                    <li><a href="/jdbc/query">SQL Query</a></li>
                    <li><a href="/jdbc/connect">JDBC Connect</a></li>
                </ul>
            </div>

            <div class="module">
                <h3>JNDI</h3>
                <ul>
                    <li><a href="/jndi/lookup">JNDI Lookup</a></li>
                </ul>
            </div>

            <div class="module">
                <h3>Memory Shell</h3>
                <ul>
                    <li><a href="/memshell/tomcat/filter">Tomcat Filter</a></li>
                    <li><a href="/memshell/spring/controller">Spring Controller</a></li>
                </ul>
            </div>

            <div class="module">
                <h3>SSRF</h3>
                <ul>
                    <li><a href="/ssrf/httpclient">HTTP Client</a></li>
                    <li><a href="/ssrf/okhttp">OkHttp</a></li>
                </ul>
            </div>

            <div class="module">
                <h3>SSTI</h3>
                <ul>
                    <li><a href="/ssti/velocity">Velocity</a></li>
                    <li><a href="/ssti/freemarker">FreeMarker</a></li>
                </ul>
            </div>

            <div class="module">
                <h3>XPath & XXE</h3>
                <ul>
                    <li><a href="/xpath/">XPath Injection</a></li>
                    <li><a href="/xxe/">XXE</a></li>
                </ul>
            </div>

            <div class="module">
                <h3>Other</h3>
                <ul>
                    <li><a href="/index/hello">Hello World</a></li>
                    <li><a href="/h2-console">H2 Database Console</a></li>
                    <li><a href="/actuator/health">Health Check</a></li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
